# -*- coding: utf-8 -*-
# by @YourName
import re
import json
import base64
import requests
from bs4 import BeautifulSoup
import sys
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def init(self, extend=""):
        self.host = "https://www.cycani.org"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

    def getName(self):
        return "cycani"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    def base64_decode(self, encoded_str):
        """
        Base64 解码
        """
        try:
            decoded_bytes = base64.b64decode(encoded_str)
            return decoded_bytes.decode("utf-8")
        except:
            return encoded_str  # 如果解码失败，返回原始字符串

    def extract_player_info(self, script_content):
        """
        从播放器脚本中提取视频信息
        """
        # 提取 JSON 数据
        json_data = re.search(r"var player_aaaa=({.*?});", script_content).group(1)
        player_info = json.loads(json_data)

        # 提取视频播放地址
        video_url = player_info.get("url")
        if video_url:
            # Base64 解码
            decoded_url = self.base64_decode(video_url)
            return decoded_url
        return None

    def homeContent(self, filter):
        result = {}
        classes = []
        videos = []

        # 分类信息（可根据需要自定义）
        cateManual = {
            "电影": "1",
            "剧集": "2",
            "综艺": "3",
            "动画": "4"
        }
        for k in cateManual:
            classes.append({
                'type_name': k,
                'type_id': cateManual[k]
            })
        result['class'] = classes

        # 首页视频列表
        response = requests.get(self.host, headers=self.headers)
        soup = BeautifulSoup(response.text, "html.parser")
        video_list = soup.find_all("div", class_="public-list-box public-pic-b")

        for video in video_list:
            title = video.find("a", class_="time-title").text.strip()
            link = self.host + video.find("a", class_="public-list-exp")["href"]
            img_tag = video.find("img", class_="lazy")
            img_url = img_tag["src"] if img_tag.get("src") else img_tag["data-src"]
            update_info = video.find("div", class_="public-list-subtitle").text.strip()

            videos.append({
                'vod_id': link.split('/')[-1].replace('.html', ''),
                'vod_name': title,
                'vod_pic': img_url,
                'vod_remarks': update_info
            })

        result['list'] = videos
        return result

    def categoryContent(self, tid, pg, filter, extend):
        result = {}
        videos = []

        # 构造分类页面URL（假设分类页面URL规则为 /type/{tid}-{pg}.html）
        url = f"{self.host}/type/{tid}-{pg}.html"
        response = requests.get(url, headers=self.headers)
        soup = BeautifulSoup(response.text, "html.parser")
        video_list = soup.find_all("div", class_="public-list-box public-pic-b")

        for video in video_list:
            title = video.find("a", class_="time-title").text.strip()
            link = self.host + video.find("a", class_="public-list-exp")["href"]
            img_tag = video.find("img", class_="lazy")
            img_url = img_tag["src"] if img_tag.get("src") else img_tag["data-src"]
            update_info = video.find("div", class_="public-list-subtitle").text.strip()

            videos.append({
                'vod_id': link.split('/')[-1].replace('.html', ''),
                'vod_name': title,
                'vod_pic': img_url,
                'vod_remarks': update_info
            })

        result['list'] = videos
        result['page'] = pg
        result['pagecount'] = 9999
        result['limit'] = 90
        result['total'] = 999999
        return result

    def detailContent(self, ids):
        result = {}
        vod_id = ids[0]
        url = f"{self.host}/bangumi/{vod_id}.html"
        response = requests.get(url, headers=self.headers)
        soup = BeautifulSoup(response.text, "html.parser")

        # 提取视频基本信息
        title = soup.find("h1").text.strip()
        img_tag = soup.find("img", class_="lazy")
        img_url = img_tag["src"] if img_tag.get("src") else img_tag["data-src"]
        update_info = soup.find("div", class_="public-list-subtitle").text.strip()
        description = soup.find("div", class_="yp_context").text.strip()

        # 提取播放列表
        play_list = soup.find("ul", class_="anthology-list-play size")
        play_urls = []
        if play_list:
            for item in play_list.find_all("li"):
                episode_name = item.find("a").text.strip()
                episode_url = self.host + item.find("a")["href"]
                play_urls.append(f"{episode_name}${episode_url}")

        # 构造视频详情
        vod = {
            'vod_id': vod_id,
            'vod_name': title,
            'vod_pic': img_url,
            'vod_remarks': update_info,
            'vod_content': description,
            'vod_play_from': 'cycani',
            'vod_play_url': '#'.join(play_urls) if play_urls else f"第1集${url}"
        }

        result['list'] = [vod]
        return result

    def searchContent(self, key, quick, pg=1):
        result = {}
        videos = []

        # 构造搜索URL（假设搜索URL规则为 /search/{key}-{pg}.html）
        url = f"{self.host}/search/{key}-{pg}.html"
        response = requests.get(url, headers=self.headers)
        soup = BeautifulSoup(response.text, "html.parser")
        video_list = soup.find_all("div", class_="public-list-box public-pic-b")

        for video in video_list:
            title = video.find("a", class_="time-title").text.strip()
            link = self.host + video.find("a", class_="public-list-exp")["href"]
            img_tag = video.find("img", class_="lazy")
            img_url = img_tag["src"] if img_tag.get("src") else img_tag["data-src"]
            update_info = video.find("div", class_="public-list-subtitle").text.strip()

            videos.append({
                'vod_id': link.split('/')[-1].replace('.html', ''),
                'vod_name': title,
                'vod_pic': img_url,
                'vod_remarks': update_info
            })

        result['list'] = videos
        result['page'] = pg
        return result

    def playerContent(self, flag, id, vipFlags):
        # 解析播放器页面
        url = f"{self.host}{id}"
        response = requests.get(url, headers=self.headers)
        soup = BeautifulSoup(response.text, "html.parser")

        # 提取播放器脚本中的视频信息
        script_tag = soup.find("script", text=re.compile(r"var player_aaaa="))
        if script_tag:
            script_content = script_tag.string
            play_url = self.extract_player_info(script_content)
            if play_url:
                return {
                    'parse': 0,  # 0 表示直接播放，1 表示需要解析
                    'url': play_url,
                    'header': self.headers
                }

        # 如果未找到播放地址，返回默认值
        return {
            'parse': 1,  # 需要解析
            'url': id,   # 使用传入的 ID 作为播放地址
            'header': self.headers
        }

    def localProxy(self, param):
        pass