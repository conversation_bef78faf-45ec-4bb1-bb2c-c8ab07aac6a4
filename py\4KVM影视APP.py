# -*- coding: utf-8 -*-
# by @开发者
import sys
import re
import json
import time
from urllib.parse import urljoin, quote
sys.path.append('..')
from base.spider import Spider


class Spider(Spider):
    def init(self, extend=""):
        self.host = "https://www.4kvm.tv"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        pass

    def getName(self):
        return "4KVM影视"

    def homeContent(self, filter):
        """获取首页内容和分类筛选"""
        try:
            # 使用getpq方法获取首页数据
            data = self.getpq()
            result = {}

            # 从导航栏解析分类
            classes = []
            # 尝试多种可能的导航选择器
            nav_selectors = [
                'banner list listitem a',
                'nav ul li a',
                'header nav a',
                '.menu a',
                'ul li a'
            ]

            nav_links = None
            for selector in nav_selectors:
                nav_links = data(selector)
                if len(nav_links) > 0:
                    break

            if nav_links:
                for link in nav_links.items():
                    href = link.attr('href')
                    text = link.text().strip()

                    # 过滤有效的分类链接
                    if href and text and href.startswith('/') and text not in ['首页', '影片下载']:
                        type_id = href.strip('/')
                        if type_id:  # 确保不是空字符串
                            classes.append({
                                'type_name': text,
                                'type_id': type_id
                            })

            # 如果没有从导航获取到分类，使用默认分类
            if not classes:
                classes = [
                    {"type_name": "电影", "type_id": "movies"},
                    {"type_name": "电视剧", "type_id": "tvshows"},
                    {"type_name": "高分电影", "type_id": "imdb"},
                    {"type_name": "热门播放", "type_id": "trending"}
                ]

            result['class'] = classes

            # 获取首页影片列表
            result['list'] = self.getlist(data('article'))

            return result

        except Exception as e:
            print(f"获取首页内容失败: {e}")
            return {"class": [], "filters": {}, "list": []}

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类内容"""
        try:
            # 构建URL
            if tid == "movies":
                url = f"{self.host}/movies"
            elif tid == "tvshows":
                url = f"{self.host}/tvshows"
            elif tid == "imdb":
                url = f"{self.host}/imdb"
            elif tid == "trending":
                url = f"{self.host}/trending"
            else:
                url = f"{self.host}/movies"
            
            # 添加分页参数
            if int(pg) > 1:
                url += f"/page/{pg}"
            
            # 添加筛选参数
            params = {}
            if extend.get("year"):
                params["year"] = extend["year"]
            if extend.get("genre"):
                params["genre"] = extend["genre"]
            
            response = self.fetch(url, params=params, headers=self.headers, timeout=10)
            html = response.text
            
            # 解析影片列表
            videos = self.parse_video_list(html)
            
            result = {
                "list": videos,
                "page": int(pg),
                "pagecount": 999,  # 设置一个较大的页数
                "limit": 20,
                "total": 99999
            }
            return result
            
        except Exception as e:
            print(f"获取分类内容失败: {e}")
            return {"list": [], "page": int(pg), "pagecount": 1, "limit": 20, "total": 0}

    def searchContent(self, key, quick, pg="1"):
        """搜索功能"""
        try:
            # 构建搜索URL
            search_url = f"{self.host}/xssearch"
            params = {"s": key}
            
            response = self.fetch(search_url, params=params, headers=self.headers, timeout=10)
            html = response.text
            
            # 解析搜索结果
            videos = self.parse_search_results(html)
            
            result = {
                "list": videos,
                "page": int(pg)
            }
            return result
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return {"list": [], "page": int(pg)}

    def detailContent(self, ids):
        """获取详情内容"""
        try:
            vod_id = ids[0]
            
            # 构建详情页URL
            detail_url = f"{self.host}/{vod_id}"
            
            response = self.fetch(detail_url, headers=self.headers, timeout=10)
            html = response.text
            
            # 解析详情信息
            vod_info = self.parse_detail_info(html, vod_id)
            
            return {"list": [vod_info]}
            
        except Exception as e:
            print(f"获取详情失败: {e}")
            return {"list": []}

    def playerContent(self, flag, id, vipFlags):
        """获取播放链接"""
        try:
            # 构建播放页面URL
            if not id.startswith('http'):
                url = urljoin(self.host, id)
            else:
                url = id

            # 获取播放页面内容
            data = self.getpq(id if id.startswith('/') else f"/{id}")

            # 解析播放源列表
            play_sources = []

            # 尝试多种可能的播放源选择器
            source_selectors = [
                '#playeroptionsul li',
                'ul.ajax_mode li',
                '.dooplay_player_option',
                'list listitem',
                '.player-list li',
                '.sources li'
            ]

            source_list = None
            for selector in source_selectors:
                source_list = data(selector)
                if len(source_list) > 0:
                    break

            if source_list:
                for item in source_list.items():
                    # 获取播放源名称和服务器
                    source_name = ''
                    source_server = ''

                    # 方法1：通过span.title和span.server获取
                    title_elem = item('span.title, .title')
                    server_elem = item('span.server, .server')

                    if title_elem:
                        source_name = title_elem.text().strip()
                    if server_elem:
                        source_server = server_elem.text().strip()

                    # 方法2：通过generic元素获取（兼容旧版本）
                    if not source_name:
                        generics = item('generic')
                        if len(generics) >= 2:
                            source_name = generics.eq(1).text().strip()
                            source_server = generics.eq(2).text().strip()

                    # 方法3：通过data属性获取播放源信息
                    data_nume = item.attr('data-nume') or ''
                    data_post = item.attr('data-post') or ''
                    data_type = item.attr('data-type') or ''

                    if source_name:
                        play_sources.append({
                            'name': source_name,
                            'server': source_server,
                            'data_nume': data_nume,
                            'data_post': data_post,
                            'data_type': data_type
                        })

            # 尝试获取真实的视频播放链接
            if play_sources:
                # 选择第一个非trailer的播放源，如果没有则选择第一个
                selected_source = None
                for source in play_sources:
                    if source['data_nume'] != 'trailer':
                        selected_source = source
                        break

                if not selected_source:
                    selected_source = play_sources[0]

                # 调用AJAX API获取真实播放链接
                real_video_url = self._get_real_video_url(selected_source)

                if real_video_url:
                    return {
                        "parse": 0,  # 直接播放，无需解析
                        "url": real_video_url,
                        "header": self.headers
                    }
                else:
                    # 如果无法获取真实链接，返回AJAX链接让框架处理
                    ajax_url = f"{self.host}/wp-admin/admin-ajax.php?action=doo_player_ajax&post={selected_source['data_post']}&nume={selected_source['data_nume']}&type={selected_source['data_type']}"
                    return {
                        "parse": 1,  # 需要解析
                        "url": ajax_url,
                        "header": self.headers
                    }
            else:
                # 如果没有找到播放源，返回原始URL
                return {
                    "parse": 1,
                    "url": url,
                    "header": self.headers
                }

        except Exception as e:
            print(f"获取播放链接失败: {e}")
            return {
                "parse": 0,
                "url": "",
                "header": {}
            }

    def _get_real_video_url(self, source):
        """从AJAX API获取真实的视频播放链接"""
        try:
            # 构建AJAX URL（使用GET参数）
            ajax_url = f"{self.host}/wp-admin/admin-ajax.php?action=doo_player_ajax&post={source['data_post']}&nume={source['data_nume']}&type={source['data_type']}"

            # 发送AJAX请求
            response = self.fetch(ajax_url, headers=self.headers)

            if response.status_code == 200:
                response_text = response.text

                # 尝试解析JSON响应
                try:
                    import json
                    json_data = json.loads(response_text)

                    # 检查常见的视频URL字段
                    video_url = None
                    for key in ['embed_url', 'url', 'src', 'link', 'video_url']:
                        if key in json_data:
                            video_url = json_data[key]
                            break

                    if video_url:
                        # 如果是iframe嵌入，尝试提取src
                        if '<iframe' in video_url:
                            import re
                            iframe_match = re.search(r'src=["\']([^"\']+)["\']', video_url)
                            if iframe_match:
                                video_url = iframe_match.group(1)

                        # 检查是否是直接可播放的视频格式
                        if any(ext in video_url.lower() for ext in ['.mp4', '.m3u8', '.flv', '.avi', '.mkv']):
                            return video_url

                        # 如果是其他格式的链接，也返回让框架处理
                        return video_url

                except json.JSONDecodeError:
                    # 如果不是JSON，尝试从HTML中提取视频链接
                    import re

                    # 查找常见的视频URL模式
                    video_patterns = [
                        r'src=["\']([^"\']*\.(?:mp4|m3u8|flv|avi|mkv)[^"\']*)["\']',
                        r'url:["\']([^"\']*\.(?:mp4|m3u8|flv|avi|mkv)[^"\']*)["\']',
                        r'file:["\']([^"\']*\.(?:mp4|m3u8|flv|avi|mkv)[^"\']*)["\']',
                        r'https?://[^"\'\s]*\.(?:mp4|m3u8|flv|avi|mkv)(?:\?[^"\'\s]*)?'
                    ]

                    for pattern in video_patterns:
                        matches = re.findall(pattern, response_text, re.IGNORECASE)
                        if matches:
                            return matches[0]

            return None

        except Exception as e:
            print(f"获取真实视频链接失败: {e}")
            return None

    def parse_video_list(self, html):
        """解析影片列表"""
        videos = []
        try:
            from pyquery import PyQuery as pq
            doc = pq(html)

            # 查找影片卡片
            articles = doc('article').items()

            for article in articles:
                try:
                    # 获取链接和标题
                    link_elem = article('a').eq(0)
                    if not link_elem:
                        continue

                    url = link_elem.attr('href')
                    if not url:
                        continue

                    # 获取标题
                    title_elem = article('.data h3 a') or article('h3 a') or link_elem
                    vod_name = title_elem.text().strip()

                    # 获取封面
                    img_elem = article('img')
                    vod_pic = img_elem.attr('src') or img_elem.attr('data-src') or ""

                    # 获取评分
                    rating_elem = article('.rating') or article('.imdb')
                    vod_remarks = rating_elem.text().strip() if rating_elem else ""

                    # 获取年份
                    year_elem = article('.year') or article('.date')
                    vod_year = year_elem.text().strip() if year_elem else ""

                    # 获取简介
                    content_elem = article('p') or article('.excerpt')
                    vod_content = content_elem.text().strip() if content_elem else ""

                    # 提取ID
                    vod_id = url.replace(self.host + "/", "").replace(self.host, "")
                    if vod_id.startswith("/"):
                        vod_id = vod_id[1:]

                    if vod_name and vod_id:
                        video = {
                            "vod_id": vod_id,
                            "vod_name": vod_name,
                            "vod_pic": vod_pic,
                            "vod_remarks": vod_remarks,
                            "vod_year": vod_year,
                            "vod_area": "",
                            "vod_director": "",
                            "vod_actor": "",
                            "vod_content": vod_content[:200] + "..." if len(vod_content) > 200 else vod_content
                        }
                        videos.append(video)

                except Exception as e:
                    print(f"解析单个影片失败: {e}")
                    continue

        except Exception as e:
            print(f"解析影片列表失败: {e}")

        return videos

    def parse_search_results(self, html):
        """解析搜索结果"""
        videos = []
        try:
            from pyquery import PyQuery as pq
            doc = pq(html)

            # 搜索结果页面的影片卡片
            articles = doc('article').items()

            for article in articles:
                try:
                    # 获取第一个链接（封面链接）
                    first_link = article('a').eq(0)
                    if not first_link:
                        continue

                    url = first_link.attr('href')
                    if not url:
                        continue

                    # 获取标题 - 从第二个链接获取（在.data区域内）
                    title_link = article('.data a') or article('a').eq(1)
                    if title_link:
                        vod_name = title_link.text().strip()
                    else:
                        # 如果没有找到标题链接，从img的alt属性获取
                        img_elem = article('img')
                        vod_name = img_elem.attr('alt') or ""

                    # 获取封面
                    img_elem = article('img')
                    vod_pic = img_elem.attr('src') or img_elem.attr('data-src') or ""

                    # 获取评分 - 查找包含IMDb的文本
                    rating_elem = article('.data').find('*').filter(lambda i, e: 'IMDb' in pq(e).text())
                    vod_remarks = rating_elem.text().strip() if rating_elem else ""

                    # 获取简介
                    content_elem = article('p')
                    vod_content = content_elem.text().strip() if content_elem else ""

                    # 提取ID
                    vod_id = url.replace(self.host + "/", "").replace(self.host, "")
                    if vod_id.startswith("/"):
                        vod_id = vod_id[1:]

                    if vod_name and vod_id:
                        video = {
                            "vod_id": vod_id,
                            "vod_name": vod_name,
                            "vod_pic": vod_pic,
                            "vod_remarks": vod_remarks,
                            "vod_year": "",
                            "vod_area": "",
                            "vod_director": "",
                            "vod_actor": "",
                            "vod_content": vod_content[:200] + "..." if len(vod_content) > 200 else vod_content
                        }
                        videos.append(video)

                except Exception as e:
                    print(f"解析单个搜索结果失败: {e}")
                    continue

        except Exception as e:
            print(f"解析搜索结果失败: {e}")

        return videos

    def parse_detail_info(self, html, vod_id):
        """解析详情信息"""
        try:
            from pyquery import PyQuery as pq
            doc = pq(html)

            # 解析标题 - 从实际HTML结构获取
            title_elem = doc('h1')
            vod_name = title_elem.text().strip() if title_elem else ""

            # 解析封面 - 第一个img标签
            poster_elem = doc('img').eq(0)
            vod_pic = poster_elem.attr('src') or poster_elem.attr('data-src') or ""

            # 解析年份和其他信息 - 从.sheader .data .extra区域
            vod_year = ""
            vod_area = ""
            vod_director = ""
            vod_actor = ""

            # 从详情信息区域提取年份、地区等
            extra_info = doc('.sheader .data .extra')
            if extra_info:
                # 获取所有子元素的文本
                for elem in extra_info.find('*').items():
                    text = elem.text().strip()
                    # 提取年份（格式如：Apr. 25, 2012）
                    year_match = re.search(r'(\w+\.\s+\d+,\s+\d{4})', text)
                    if year_match:
                        vod_year = year_match.group(1)
                        break
                    # 也尝试匹配纯年份格式
                    elif re.match(r'^\d{4}$', text):
                        vod_year = text
                        break

                # 提取地区信息
                info_text = extra_info.text()
                if 'USA' in info_text:
                    vod_area = "USA"
                elif 'China' in info_text:
                    vod_area = "China"
                elif 'Japan' in info_text:
                    vod_area = "Japan"

            # 解析评分 - 从评分区域获取
            rating_elem = doc('.dt_rating_vgs')
            vod_remarks = rating_elem.text().strip() if rating_elem else ""

            # 解析简介 - 从wp-content区域的p标签
            content_elem = doc('.wp-content p').filter(lambda i, e: len(pq(e).text()) > 50)
            vod_content = content_elem.eq(0).text().strip() if content_elem else ""

            # 解析播放源 - 从实际HTML结构获取
            play_sources = []
            play_urls = []

            # 查找播放源列表 - 从ul li结构中获取
            player_list = doc('ul li').filter(lambda i, e: pq(e).find('.title').length > 0 or 'player' in pq(e).attr('class', ''))

            if player_list:
                for i, item in enumerate(player_list.items()):
                    # 获取播放源名称
                    title_elem = item.find('.title') or item
                    source_name = title_elem.text().strip()

                    if not source_name:
                        source_name = f"播放源{i+1}"

                    play_sources.append(source_name)

                    # 构建播放链接 - 使用vod_id作为基础
                    play_url = f"播放${vod_id}"
                    play_urls.append(play_url)
            else:
                # 如果没有找到播放源，使用默认
                play_sources = ["默认播放源"]
                play_urls = [f"播放${vod_id}"]

            # 确保播放源和播放链接数量一致
            while len(play_urls) < len(play_sources):
                play_urls.append(f"播放${vod_id}")

            vod_play_from = "$$$".join(play_sources)
            vod_play_url = "$$$".join(play_urls)

            vod_info = {
                "vod_id": vod_id,
                "vod_name": vod_name,
                "vod_pic": vod_pic,
                "vod_content": vod_content,
                "vod_year": vod_year,
                "vod_area": "",
                "vod_director": vod_director,
                "vod_actor": vod_actor,
                "vod_play_from": vod_play_from,
                "vod_play_url": vod_play_url,
                "vod_remarks": vod_remarks
            }

            return vod_info

        except Exception as e:
            print(f"解析详情信息失败: {e}")
            return {
                "vod_id": vod_id,
                "vod_name": "解析失败",
                "vod_pic": "",
                "vod_content": "",
                "vod_year": "",
                "vod_area": "",
                "vod_director": "",
                "vod_actor": "",
                "vod_play_from": "默认播放源",
                "vod_play_url": f"播放${vod_id}",
                "vod_remarks": ""
            }

    def getpq(self, path=''):
        """获取页面PyQuery对象的辅助方法"""
        try:
            from pyquery import PyQuery as pq
            url = f"{self.host}{path}" if path else self.host
            data = self.fetch(url, headers=self.headers).text
            try:
                return pq(data)
            except Exception as e:
                print(f"PyQuery解析失败: {e}")
                return pq(data.encode('utf-8'))
        except Exception as e:
            print(f"获取页面数据失败: {e}")
            from pyquery import PyQuery as pq
            return pq("")

    def getlist(self, data, year_selector=''):
        """从页面数据中提取影片列表的辅助方法"""
        videos = []
        try:
            for item in data.items():
                # 提取链接
                link_elem = item('a').eq(0)
                if not link_elem:
                    continue

                vod_id = link_elem.attr('href')
                if not vod_id:
                    continue

                # 确保vod_id是相对路径格式，符合PyramidStore框架要求
                if vod_id.startswith('http'):
                    # 从完整URL中提取相对路径
                    from urllib.parse import urlparse
                    parsed = urlparse(vod_id)
                    vod_id = parsed.path.lstrip('/')  # 移除开头的斜杠
                elif vod_id.startswith('/'):
                    vod_id = vod_id.lstrip('/')  # 移除开头的斜杠

                # 提取标题
                title_elem = item('h3, h2, .title, [class*="title"]').eq(0)
                if not title_elem:
                    title_elem = link_elem
                vod_name = title_elem.text().strip()

                # 提取图片
                img_elem = item('img').eq(0)
                vod_pic = img_elem.attr('src') if img_elem else ''

                # 提取年份或备注信息
                vod_remarks = ''
                vod_year = ''

                if year_selector:
                    year_elem = item(year_selector).eq(0)
                    if year_elem:
                        vod_year = year_elem.text().strip()

                # 如果没有指定年份选择器，尝试从标题中提取年份
                if not vod_year and vod_name:
                    year_match = re.search(r'(\d{4})', vod_name)
                    if year_match:
                        vod_year = year_match.group(1)

                if vod_id and vod_name:
                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_remarks': vod_remarks,
                        'vod_year': vod_year
                    })

        except Exception as e:
            print(f"解析影片列表失败: {e}")

        return videos

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    def localProxy(self, param):
        pass


# 测试代码
if __name__ == "__main__":
    spider = Spider()
    spider.init("")
    
    print("=== 测试首页内容 ===")
    home_result = spider.homeContent(False)
    print(f"分类数量: {len(home_result.get('class', []))}")
    print(f"筛选条件: {len(home_result.get('filters', {}))}")
    
    print("\n=== 测试搜索功能 ===")
    search_result = spider.searchContent("测试", False, "1")
    print(f"搜索结果数量: {len(search_result.get('list', []))}")
    
    if search_result.get('list'):
        print("\n=== 测试详情页 ===")
        first_item = search_result['list'][0]
        detail_result = spider.detailContent([first_item['vod_id']])
        print(f"详情获取成功: {len(detail_result.get('list', []))}")
