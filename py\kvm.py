# coding=utf-8
# !/usr/bin/python
import sys
sys.path.append('..')
from base.spider import Spider
import json
import time
import urllib.parse

class Spider(Spider):  # 元类 默认的元类 type
    
    def getName(self):
        return "4KVM影视"
    
    def init(self, extend=""):
        self.host = "https://www.4kvm.tv"
        # 备用域名（包含去掉www的版本）
        self.backup_hosts = [
            "https://4kvm.tv",
            "http://4kvm.tv",
            "https://4kvm.net",
            "http://4kvm.net",
            "https://www.4kvm.net",
            "http://www.4kvm.tv",
            "http://www.4kvm.net"
        ]
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': self.host
        }

        # 代理配置（可选）
        self.proxies = None  # 可以设置为 {'http': 'proxy_url', 'https': 'proxy_url'}

        # 请求间隔配置
        self.request_delay = (1, 3)  # 随机延迟1-3秒

        self.log(f"4KVM影视爬虫初始化完成，主站: {self.host}")

    def _get_random_delay(self):
        """获取随机延迟时间"""
        import random
        return random.uniform(self.request_delay[0], self.request_delay[1])

    def _should_use_proxy(self):
        """判断是否应该使用代理"""
        return self.proxies is not None

    def _fetch_with_retry(self, url, max_retries=2):
        """带反爬虫绕过的网络请求（模拟真实浏览器）"""
        import requests
        import random
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        import urllib3

        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 真实浏览器User-Agent池
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        hosts_to_try = [self.host] + self.backup_hosts

        for host in hosts_to_try:
            # 替换URL中的主机名
            if url.startswith(self.host):
                test_url = url.replace(self.host, host)
            else:
                test_url = host + url if url.startswith('/') else url

            for retry in range(max_retries + 1):
                try:
                    self.log(f"模拟浏览器访问: {test_url} (重试: {retry}/{max_retries})")

                    # 创建新的会话
                    session = requests.Session()

                    # 随机选择User-Agent
                    user_agent = random.choice(user_agents)

                    # 构建完整的浏览器头部
                    headers = {
                        'User-Agent': user_agent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Sec-Fetch-User': '?1',
                        'Cache-Control': 'max-age=0',
                        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"'
                    }

                    # 如果不是第一次访问，添加Referer
                    if retry > 0:
                        headers['Referer'] = host

                    # 配置代理（如果有）
                    proxies = self.proxies if self._should_use_proxy() else None

                    # 模拟真实浏览器行为
                    response = session.get(
                        test_url,
                        headers=headers,
                        timeout=25,
                        verify=False,  # 禁用SSL验证
                        allow_redirects=True,
                        proxies=proxies,
                        stream=False
                    )

                    # 检查响应
                    if response.status_code == 200:
                        # 检查是否被重定向到错误页面
                        if '404' not in response.text and 'Not Found' not in response.text:
                            self.log(f"✅ 成功访问: {test_url}")
                            self.log(f"   响应大小: {len(response.text)} 字符")

                            # 更新当前可用的主机
                            if host != self.host:
                                self.host = host
                                self.log(f"   切换到可用域名: {host}")
                            return response
                        else:
                            self.log(f"❌ 页面返回404错误")
                    else:
                        self.log(f"❌ HTTP错误: {response.status_code}")

                except Exception as e:
                    self.log(f"❌ 请求失败: {str(e)}")

                # 重试前的延迟
                if retry < max_retries:
                    delay = random.uniform(3, 8)  # 更长的延迟
                    self.log(f"⏳ 等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)

        # 所有尝试都失败了
        self.log(f"❌ 所有域名和重试都失败了")
        return None

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def homeContent(self, filter):
        """首页内容"""
        try:
            self.log("开始获取首页内容...")
            start_time = time.time()

            # 使用标准fetch方法（如果环境支持）
            try:
                rsp = self.fetch(self.host, headers=self.headers)
                if rsp and rsp.text:
                    self.log("使用标准fetch方法成功")
                else:
                    raise Exception("标准fetch失败")
            except:
                # 回退到带重试机制的请求
                self.log("标准fetch失败，使用重试机制...")
                rsp = self._fetch_with_retry(self.host)
                if not rsp:
                    self.log("无法访问首页，返回空列表")
                    return {'list': []}

            doc = self.html(rsp.text)

            # 提取首页视频列表
            videos = self._get_home_videos(doc)

            end_time = time.time()
            self.log(f"首页获取完成，共{len(videos)}个视频，耗时{end_time-start_time:.2f}秒")

            result = {
                'list': videos
            }
            return result

        except Exception as e:
            self.log(f"首页获取出错: {str(e)}")
            return {'list': []}

    def homeVideoContent(self):
        """分类定义"""
        try:
            result = {
                'class': [
                    {'type_id': '1', 'type_name': '电影'},
                    {'type_id': '2', 'type_name': '电视剧'},
                    {'type_id': '3', 'type_name': '美剧'},
                    {'type_id': '4', 'type_name': '国产剧'},
                    {'type_id': '5', 'type_name': '韩剧'},
                    {'type_id': '6', 'type_name': '番剧'},
                    {'type_id': '7', 'type_name': '高分电影'},
                    {'type_id': '8', 'type_name': '热门播放'}
                ]
            }
            self.log(f"分类定义完成，共{len(result['class'])}个分类")
            return result
            
        except Exception as e:
            self.log(f"分类定义出错: {str(e)}")
            return {'class': []}

    def categoryContent(self, tid, pg, filter, extend):
        """分类内容"""
        try:
            self.log(f"获取分类内容: tid={tid}, pg={pg}")
            start_time = time.time()
            
            # 根据分类ID构建URL
            url = self._get_category_url(tid, pg)
            if not url:
                return {'list': []}
                
            rsp = self.fetch(url, headers=self.headers)
            doc = self.html(rsp.text)
            
            # 提取分类页面视频列表
            videos = self._get_category_videos(doc)
            
            end_time = time.time()
            self.log(f"分类内容获取完成，共{len(videos)}个视频，耗时{end_time-start_time:.2f}秒")
            
            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': 999,  # 暂时设置为999
                'limit': 20,
                'total': 999
            }
            return result
            
        except Exception as e:
            self.log(f"分类内容获取出错: {str(e)}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        """搜索功能"""
        try:
            self.log(f"搜索关键词: {key}, 页码: {pg}")
            start_time = time.time()
            
            # 构建搜索URL (需要根据实际网站搜索接口调整)
            search_url = f"{self.host}/search?q={urllib.parse.quote(key)}&page={pg}"
            
            rsp = self.fetch(search_url, headers=self.headers)
            doc = self.html(rsp.text)
            
            # 提取搜索结果
            videos = self._get_search_videos(doc)
            
            end_time = time.time()
            self.log(f"搜索完成，共{len(videos)}个结果，耗时{end_time-start_time:.2f}秒")
            
            return {'list': videos}
            
        except Exception as e:
            self.log(f"搜索出错: {str(e)}")
            return {'list': []}

    def detailContent(self, ids):
        """详情页面"""
        try:
            vid = ids[0]
            self.log(f"获取详情页面: {vid}")
            start_time = time.time()
            
            # 构建详情页URL
            detail_url = self._get_detail_url(vid)
            if not detail_url:
                return {'list': []}
                
            rsp = self.fetch(detail_url, headers=self.headers)
            doc = self.html(rsp.text)
            
            # 提取详情信息
            video_info = self._get_video_detail(doc, vid)
            
            end_time = time.time()
            self.log(f"详情获取完成，耗时{end_time-start_time:.2f}秒")
            
            return {'list': [video_info]} if video_info else {'list': []}
            
        except Exception as e:
            self.log(f"详情获取出错: {str(e)}")
            return {'list': []}

    def playerContent(self, flag, id, vipFlags):
        """播放链接"""
        try:
            self.log(f"获取播放链接: flag={flag}, id={id}")
            start_time = time.time()
            
            # 构建播放页URL
            play_url = self._get_play_url(id)
            if not play_url:
                return {}
                
            rsp = self.fetch(play_url, headers=self.headers)
            
            # 提取真实播放链接
            real_url = self._extract_play_url(rsp.text)
            
            end_time = time.time()
            self.log(f"播放链接获取完成，耗时{end_time-start_time:.2f}秒")
            
            result = {
                'parse': 0,
                'playUrl': '',
                'url': real_url
            }
            return result
            
        except Exception as e:
            self.log(f"播放链接获取出错: {str(e)}")
            return {}

    # ========== 辅助方法 ==========
    
    def _get_home_videos(self, doc):
        """提取首页视频列表"""
        try:
            videos = []
            seen_ids = set()

            # 基于实际HTML结构提取视频信息
            # 1. 从侧边栏"本周热门"提取
            hot_elements = doc.xpath('//h2[contains(text(),"本周热门")]/following-sibling::div//article[@class="w_item_b"]')
            if hot_elements:
                self.log(f"本周热门区域找到 {len(hot_elements)} 个元素")
                for elem in hot_elements:
                    video = self._extract_sidebar_video_info(elem, "w_item_b")
                    if video and video['vod_id'] not in seen_ids:
                        videos.append(video)
                        seen_ids.add(video['vod_id'])

            # 2. 从侧边栏"最近更新"提取
            recent_elements = doc.xpath('//h2[contains(text(),"最近更新")]/following-sibling::div//article[@class="w_item_c"]')
            if recent_elements:
                self.log(f"最近更新区域找到 {len(recent_elements)} 个元素")
                for elem in recent_elements:
                    video = self._extract_sidebar_video_info(elem, "w_item_c")
                    if video and video['vod_id'] not in seen_ids:
                        videos.append(video)
                        seen_ids.add(video['vod_id'])

            # 3. 从主内容区域提取（如果有的话）
            main_elements = doc.xpath('//div[@class="items"]//article[contains(@class,"item")]')
            if main_elements:
                self.log(f"主内容区域找到 {len(main_elements)} 个元素")
                for elem in main_elements:
                    video = self._extract_main_video_info(elem)
                    if video and video['vod_id'] not in seen_ids:
                        videos.append(video)
                        seen_ids.add(video['vod_id'])

            # 4. 如果以上都没找到，尝试通用选择器
            if not videos:
                self.log("使用通用选择器提取视频")
                general_elements = doc.xpath('//a[contains(@href,"/movies/") or contains(@href,"/tvshows/")]')

                for elem in general_elements[:30]:
                    # 获取父元素作为视频卡片
                    parent = elem.getparent()
                    if parent is not None:
                        video = self._extract_video_info(parent)
                        if video and video['vod_id'] not in seen_ids:
                            videos.append(video)
                            seen_ids.add(video['vod_id'])
                            if len(videos) >= 20:
                                break

            self.log(f"首页共提取到 {len(videos)} 个视频")
            return videos[:20]  # 限制20个

        except Exception as e:
            self.log(f"提取首页视频出错: {str(e)}")
            return []

    def _extract_video_info(self, element):
        """通用视频信息提取"""
        try:
            # 提取链接和ID
            links = element.xpath('.//a[contains(@href,"/movies/") or contains(@href,"/tvshows/")]/@href')
            if not links:
                return None
                
            link = links[0]
            if link.startswith('/'):
                link = self.host + link
                
            # 提取ID
            vod_id = self.regStr(r'/(?:movies|tvshows|seasons)/([^/]+)', link)
            if not vod_id:
                return None
            
            # 提取标题
            title = self._extract_title(element)
            if not title:
                return None
            
            # 提取图片
            pic = self._extract_image(element)
            
            # 提取状态/年份信息
            remarks = self._extract_remarks(element)
            
            return {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': remarks,
                'vod_year': ''
            }
            
        except Exception as e:
            self.log(f"提取视频信息出错: {str(e)}")
            return None

    def _extract_title(self, element):
        """提取标题"""
        try:
            # 基于实际HTML结构的标题提取策略
            title_selectors = [
                './/h3/text()',
                './/h3/a/text()',
                './/a[contains(@href,"/movies/") or contains(@href,"/tvshows/")]/text()',
                './/a/@title',
                './/div[contains(@class,"title")]/text()',
                './/span[contains(@class,"title")]/text()',
                './/div[@class="title"]/text()',
                './/p[contains(@class,"title")]/text()'
            ]

            for selector in title_selectors:
                titles = element.xpath(selector)
                for t in titles:
                    if t and t.strip() and len(t.strip()) > 1:
                        # 过滤掉一些无效的标题
                        title = t.strip()
                        if title not in ['更多', 'More', '详情', 'Detail', '播放', 'Play']:
                            return title

            return ''

        except Exception as e:
            self.log(f"提取标题出错: {str(e)}")
            return ''

    def _extract_image(self, element):
        """提取图片"""
        try:
            # 基于实际HTML结构的图片提取策略
            pic_selectors = [
                './/img/@data-src',
                './/img/@src',
                './/img/@data-original',
                './/img/@data-lazy-src'
            ]

            for selector in pic_selectors:
                pics = element.xpath(selector)
                for p in pics:
                    if p and not p.startswith('data:image/') and 'base64' not in p:
                        # 跳过占位符图片
                        if 'placeholder' in p.lower() or 'loading' in p.lower():
                            continue

                        # 使用标准化方法处理图片URL
                        normalized_pic = self._normalize_image_url(p)
                        if normalized_pic:
                            return normalized_pic

            return ''

        except Exception as e:
            self.log(f"提取图片出错: {str(e)}")
            return ''

    def _extract_remarks(self, element):
        """提取状态信息"""
        try:
            # 提取年份、评分、状态等信息
            remarks_selectors = [
                './/span/text()',
                './/div[contains(@class,"year")]/text()',
                './/div[contains(@class,"date")]/text()',
                './/p/text()',
                './/small/text()',
                './/time/text()',
                './/span[contains(@class,"rating")]/text()',
                './/div[contains(@class,"rating")]/text()'
            ]

            for selector in remarks_selectors:
                remarks = element.xpath(selector)
                for r in remarks:
                    if r and r.strip():
                        text = r.strip()
                        # 过滤掉标题等无关信息
                        if (len(text) <= 20 and
                            ('20' in text or '19' in text or  # 年份
                             '.' in text or  # 评分
                             '集' in text or '季' in text or  # 集数/季数
                             'HD' in text or 'BD' in text or  # 清晰度
                             '完结' in text or '更新' in text)):  # 状态
                            return text

            return ''

        except Exception as e:
            self.log(f"提取状态信息出错: {str(e)}")
            return ''

    def _get_category_url(self, tid, pg):
        """根据分类ID构建URL"""
        try:
            category_map = {
                '1': '/movies',
                '2': '/tvshows',
                '3': '/classify/meiju',
                '4': '/classify/guochan',
                '5': '/classify/hanju',
                '6': '/classify/fanju',
                '7': '/imdb',
                '8': '/trending'
            }

            base_url = category_map.get(tid)
            if not base_url:
                return None

            # 添加分页参数 (需要根据实际网站调整)
            if pg and pg != '1':
                url = f"{self.host}{base_url}?page={pg}"
            else:
                url = f"{self.host}{base_url}"

            return url

        except Exception as e:
            self.log(f"构建分类URL出错: {str(e)}")
            return None

    def _get_category_videos(self, doc):
        """提取分类页面视频"""
        try:
            videos = []
            seen_ids = set()

            # 基于实际分类页面结构提取视频
            # 方法1: 从主内容区域提取 (div.items article.item)
            main_elements = doc.xpath('//div[@class="items"]//article[contains(@class,"item")]')
            if main_elements:
                self.log(f"分类页面主内容区域找到 {len(main_elements)} 个视频元素")
                for elem in main_elements:
                    video = self._extract_main_video_info(elem)
                    if video and video['vod_id'] not in seen_ids:
                        videos.append(video)
                        seen_ids.add(video['vod_id'])

            # 方法2: 从侧边栏"本周热门"补充
            if len(videos) < 10:
                hot_elements = doc.xpath('//h2[contains(text(),"本周热门")]/following-sibling::div//article[@class="w_item_b"]')
                if hot_elements:
                    self.log(f"从侧边栏本周热门补充 {len(hot_elements)} 个视频")
                    for elem in hot_elements:
                        video = self._extract_sidebar_video_info(elem, "w_item_b")
                        if video and video['vod_id'] not in seen_ids:
                            videos.append(video)
                            seen_ids.add(video['vod_id'])

            # 方法3: 从侧边栏"最近更新"补充
            if len(videos) < 15:
                recent_elements = doc.xpath('//h2[contains(text(),"最近更新")]/following-sibling::div//article[@class="w_item_c"]')
                if recent_elements:
                    self.log(f"从侧边栏最近更新补充 {len(recent_elements)} 个视频")
                    for elem in recent_elements:
                        video = self._extract_sidebar_video_info(elem, "w_item_c")
                        if video and video['vod_id'] not in seen_ids:
                            videos.append(video)
                            seen_ids.add(video['vod_id'])

            # 方法4: 如果还是没找到，尝试通用方法
            if not videos:
                self.log("分类页面未找到视频，使用通用方法")
                videos = self._get_home_videos(doc)

            self.log(f"分类页面共提取到 {len(videos)} 个视频")
            return videos

        except Exception as e:
            self.log(f"提取分类视频出错: {str(e)}")
            return []

    def _get_search_videos(self, doc):
        """提取搜索结果"""
        try:
            videos = []
            seen_ids = set()

            # 基于实际搜索页面结构提取
            # 搜索结果在 div.result-item 中
            search_elements = doc.xpath('//div[@class="result-item"]')

            if search_elements:
                self.log(f"搜索结果找到 {len(search_elements)} 个元素")
                for elem in search_elements:
                    video = self._extract_search_video_info(elem)
                    if video and video['vod_id'] not in seen_ids:
                        videos.append(video)
                        seen_ids.add(video['vod_id'])
            else:
                self.log("未找到搜索结果，尝试通用方法")
                # 回退到通用方法
                videos = self._get_home_videos(doc)

            self.log(f"搜索共提取到 {len(videos)} 个视频")
            return videos

        except Exception as e:
            self.log(f"提取搜索结果出错: {str(e)}")
            return []

    def _extract_search_video_info(self, element):
        """提取搜索结果中的视频信息"""
        try:
            # 提取链接和ID
            link_elem = element.xpath('.//a[contains(@href,"/movies/") or contains(@href,"/tvshows/")]')[0]
            link = link_elem.get('href')

            # 提取视频ID
            vod_id = self.regStr(r'/(?:movies|tvshows)/([^/?]+)', link)
            if not vod_id:
                return None

            # 提取标题
            title_elem = element.xpath('.//div[@class="title"]/a')
            title = title_elem[0].text.strip() if title_elem else ''

            # 提取图片
            img_elem = element.xpath('.//img')
            pic = ''
            if img_elem:
                pic = img_elem[0].get('src', '')
                pic = self._normalize_image_url(pic)

            # 提取评分/状态信息
            rating_elem = element.xpath('.//span[@class="rating"]')
            remarks = rating_elem[0].text.strip() if rating_elem else ''

            # 提取类型信息
            type_elem = element.xpath('.//span[@class="movies" or @class="tvshows"]')
            if type_elem:
                type_text = type_elem[0].text.strip()
                if not remarks:
                    remarks = type_text

            return {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': remarks,
                'vod_year': ''
            }

        except Exception as e:
            self.log(f"提取搜索视频信息出错: {str(e)}")
            return None

    def _normalize_image_url(self, pic):
        """标准化图片URL"""
        try:
            if not pic:
                return ''

            # 处理相对路径
            if pic.startswith('//'):
                return 'https:' + pic
            elif pic.startswith('/'):
                return self.host + pic
            elif pic.startswith('./'):
                # 处理本地保存的相对路径，转换为实际的图片URL
                # 例如: ./4k影视 - 4k影视搜索_files/xxx.jpg
                # 需要根据实际情况转换为真实的图片URL
                if 'tmdb.org' in pic or 'baidu.com' in pic:
                    # 如果包含已知的图片服务域名，尝试提取
                    import re
                    match = re.search(r'(https?://[^"\'>\s]+\.(?:jpg|jpeg|png|gif|webp))', pic)
                    if match:
                        return match.group(1)

                # 否则构建一个占位符URL
                filename = pic.split('/')[-1] if '/' in pic else pic
                if filename.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                    # 使用TMDB的图片服务作为占位符
                    return f"https://image.tmdb.org/t/p/w500/{filename}"

                return ''
            elif pic.startswith('http'):
                return pic
            else:
                # 可能是相对路径
                return self.host + '/' + pic.lstrip('/')

        except Exception as e:
            self.log(f"标准化图片URL出错: {str(e)}")
            return pic

    def _extract_main_video_info(self, element):
        """提取主内容区域的视频信息 (article.item)"""
        try:
            # 提取链接和ID
            link_elem = element.xpath('.//a[contains(@href,"/movies/") or contains(@href,"/tvshows/")]')
            if not link_elem:
                return None

            link = link_elem[0].get('href')
            vod_id = self.regStr(r'/(?:movies|tvshows)/([^/?]+)', link)
            if not vod_id:
                return None

            # 提取标题 - 从 div.data h3 a
            title_elem = element.xpath('.//div[@class="data"]/h3/a/text()')
            title = title_elem[0].strip() if title_elem else ''

            # 提取图片 - 从 div.poster img
            img_elem = element.xpath('.//div[@class="poster"]/img/@src')
            pic = ''
            if img_elem:
                pic = self._normalize_image_url(img_elem[0])

            # 提取评分 - 从 div.rating
            rating_elem = element.xpath('.//div[@class="rating"]/text()')
            rating = ''
            if rating_elem:
                rating_text = rating_elem[0].strip()
                rating = rating_text

            # 提取更新信息 - 从 div.update (电视剧特有)
            update_elem = element.xpath('.//div[@class="update"]/text()')
            update_info = update_elem[0].strip() if update_elem else ''

            # 提取日期 - 从 div.data span
            date_elem = element.xpath('.//div[@class="data"]/span/text()')
            date = date_elem[0].strip() if date_elem else ''

            # 构建状态信息
            remarks_parts = []
            if rating:
                remarks_parts.append(f"评分 {rating}")
            if update_info:
                remarks_parts.append(update_info)
            if date:
                year_match = self.regStr(r'(\d{4})', date)
                if year_match:
                    remarks_parts.append(year_match)

            remarks = ' | '.join(remarks_parts) if remarks_parts else ''

            # 提取年份
            year = ''
            if date:
                year_match = self.regStr(r'(\d{4})', date)
                if year_match:
                    year = year_match

            return {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': remarks,
                'vod_year': year
            }

        except Exception as e:
            self.log(f"提取主内容视频信息出错: {str(e)}")
            return None

    def _extract_sidebar_video_info(self, element, item_type):
        """提取侧边栏视频信息 (w_item_b, w_item_c)"""
        try:
            # 提取链接和ID
            link_elem = element.xpath('.//a[contains(@href,"/movies/") or contains(@href,"/tvshows/")]')
            if not link_elem:
                return None

            link = link_elem[0].get('href')
            vod_id = self.regStr(r'/(?:movies|tvshows)/([^/?]+)', link)
            if not vod_id:
                return None

            # 提取标题
            title_elem = element.xpath('.//h3/text() | .//div[@class="name"]/text()')
            title = title_elem[0].strip() if title_elem else ''

            # 提取图片
            img_elem = element.xpath('.//img/@src')
            pic = ''
            if img_elem:
                pic = self._normalize_image_url(img_elem[0])

            # 提取评分 (w_item_c 特有)
            rating_elem = element.xpath('.//div[@class="rating"]/text()')
            rating = ''
            if rating_elem:
                rating_text = rating_elem[0].strip()
                rating = rating_text

            # 提取日期/年份
            date_elem = element.xpath('.//span[@class="wdate"]/text() | .//span[contains(@class,"date")]/text()')
            date = date_elem[0].strip() if date_elem else ''

            # 构建状态信息
            remarks_parts = []
            if rating:
                remarks_parts.append(f"评分 {rating}")
            if date:
                remarks_parts.append(date)

            remarks = ' | '.join(remarks_parts) if remarks_parts else ''

            # 提取年份
            year = ''
            if date:
                year_match = self.regStr(r'(\d{4})', date)
                if year_match:
                    year = year_match

            return {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': remarks,
                'vod_year': year
            }

        except Exception as e:
            self.log(f"提取侧边栏视频信息出错: {str(e)}")
            return None

    def _get_detail_url(self, vid):
        """构建详情页URL"""
        try:
            # 需要判断是电影还是电视剧
            # 暂时假设格式，后续根据实际情况调整
            return f"{self.host}/movies/{vid}"

        except Exception as e:
            self.log(f"构建详情URL出错: {str(e)}")
            return None

    def _get_video_detail(self, doc, vid):
        """提取视频详情"""
        try:
            # 基于实际详情页面结构提取信息

            # 提取标题
            title_elem = doc.xpath('//div[@class="data"]/h1/text() | //h1/text()')
            title = title_elem[0].strip() if title_elem else vid

            # 提取海报图片
            poster_elem = doc.xpath('//div[@class="poster"]/img/@src')
            pic = ''
            if poster_elem:
                pic = poster_elem[0]
                if pic.startswith('//'):
                    pic = 'https:' + pic
                elif pic.startswith('/'):
                    pic = self.host + pic

            # 提取评分
            rating_elem = doc.xpath('//div[@class="dt_rating_data"]//input[@name="score"]/@value')
            rating = rating_elem[0] if rating_elem else ''

            # 提取年份
            year_elem = doc.xpath('//span[@class="date"]/text() | //span[contains(@class,"year")]/text()')
            year = ''
            if year_elem:
                year_text = year_elem[0].strip()
                year_match = self.regStr(r'(\d{4})', year_text)
                if year_match:
                    year = year_match

            # 提取地区/类型信息
            extra_elem = doc.xpath('//div[@class="extra"]//span/text()')
            area = ''
            if extra_elem:
                for text in extra_elem:
                    if any(country in text for country in ['美国', '中国', '日本', '韩国', '英国', '法国']):
                        area = text.strip()
                        break

            # 提取演员信息
            actor_elems = doc.xpath('//div[@class="persons"]//div[@class="person"]//div[@class="name"]/a/text()')
            actors = ', '.join(actor_elems[:5]) if actor_elems else ''

            # 提取导演信息
            director_elems = doc.xpath('//div[@class="persons"]//div[contains(@class,"director")]//div[@class="name"]/a/text()')
            director = ', '.join(director_elems) if director_elems else ''

            # 提取剧情简介
            content_elem = doc.xpath('//div[@class="wp-content"]/p/text()')
            content = content_elem[0].strip() if content_elem else ''

            # 提取播放源和播放链接
            play_from, play_url = self._extract_play_info(doc)

            # 构建状态信息
            remarks = rating if rating else year

            return {
                'vod_id': vid,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': remarks,
                'vod_year': year,
                'vod_area': area,
                'vod_director': director,
                'vod_actor': actors,
                'vod_content': content,
                'vod_play_from': play_from,
                'vod_play_url': play_url
            }

        except Exception as e:
            self.log(f"提取视频详情出错: {str(e)}")
            return None

    def _extract_play_info(self, doc):
        """提取播放源和播放链接信息"""
        try:
            play_from_list = []
            play_url_list = []

            # 提取播放列表
            episodes_elem = doc.xpath('//div[@id="episodes"]')
            if episodes_elem:
                # 提取季信息
                seasons = doc.xpath('//div[@id="seasons"]//div[@class="se-c"]')

                for season in seasons:
                    # 提取季标题
                    season_title_elem = season.xpath('.//span[@class="title"]/text()')
                    season_title = season_title_elem[0].strip() if season_title_elem else '第1季'

                    # 提取季链接
                    season_link_elem = season.xpath('.//a/@href')
                    if season_link_elem:
                        season_url = season_link_elem[0]
                        if season_url.startswith('/'):
                            season_url = self.host + season_url

                        play_from_list.append(season_title)
                        play_url_list.append(f"第1集${season_url}")

            # 如果没有找到播放信息，尝试其他方法
            if not play_from_list:
                # 查找直接的播放链接
                play_links = doc.xpath('//a[contains(@href,"/seasons/") or contains(@href,"/play/")]')
                if play_links:
                    play_from_list.append('默认播放源')
                    urls = []
                    for i, link in enumerate(play_links[:10]):
                        href = link.get('href', '')
                        if href.startswith('/'):
                            href = self.host + href
                        title = link.text or f'第{i+1}集'
                        urls.append(f"{title}${href}")
                    play_url_list.append('#'.join(urls))

            play_from = '$$$'.join(play_from_list) if play_from_list else ''
            play_url = '$$$'.join(play_url_list) if play_url_list else ''

            return play_from, play_url

        except Exception as e:
            self.log(f"提取播放信息出错: {str(e)}")
            return '', ''

    def _get_play_url(self, id):
        """构建播放页URL"""
        try:
            # 播放页面URL格式分析
            # 通常格式为: /seasons/{id} 或 /play/{id}
            if '/' in id:
                # 如果ID中包含路径，直接使用
                if id.startswith('http'):
                    return id
                elif id.startswith('/'):
                    return self.host + id
                else:
                    return self.host + '/' + id
            else:
                # 尝试不同的播放页面格式
                possible_urls = [
                    f"{self.host}/seasons/{id}",
                    f"{self.host}/play/{id}",
                    f"{self.host}/watch/{id}"
                ]
                return possible_urls[0]  # 返回第一个尝试

        except Exception as e:
            self.log(f"构建播放URL出错: {str(e)}")
            return None

    def _extract_play_url(self, html):
        """提取真实播放链接"""
        try:
            # 基于播放页面结构提取真实播放链接
            doc = self.html(html)

            # 方法1: 查找iframe中的播放链接
            iframe_elems = doc.xpath('//iframe/@src')
            for iframe_src in iframe_elems:
                if any(domain in iframe_src for domain in ['player', 'play', 'video']):
                    self.log(f"找到iframe播放链接: {iframe_src}")
                    return iframe_src

            # 方法2: 查找JavaScript中的播放链接
            script_elems = doc.xpath('//script/text()')
            for script in script_elems:
                # 查找常见的播放链接模式
                patterns = [
                    r'["\']url["\']:\s*["\']([^"\']+)["\']',
                    r'["\']src["\']:\s*["\']([^"\']+)["\']',
                    r'player\.src\s*=\s*["\']([^"\']+)["\']',
                    r'video\.src\s*=\s*["\']([^"\']+)["\']'
                ]

                for pattern in patterns:
                    match = self.regStr(pattern, script)
                    if match and ('http' in match or match.startswith('//')):
                        self.log(f"从JavaScript中找到播放链接: {match}")
                        if match.startswith('//'):
                            return 'https:' + match
                        return match

            # 方法3: 查找video标签
            video_elems = doc.xpath('//video/@src | //video/source/@src')
            for video_src in video_elems:
                if video_src:
                    self.log(f"找到video标签播放链接: {video_src}")
                    return video_src

            # 方法4: 查找data属性中的播放链接
            data_elems = doc.xpath('//*[@data-src or @data-url or @data-video]')
            for elem in data_elems:
                for attr in ['data-src', 'data-url', 'data-video']:
                    url = elem.get(attr)
                    if url and ('http' in url or url.startswith('//')):
                        self.log(f"找到data属性播放链接: {url}")
                        if url.startswith('//'):
                            return 'https:' + url
                        return url

            self.log("未找到播放链接")
            return ''

        except Exception as e:
            self.log(f"提取播放链接出错: {str(e)}")
            return ''
