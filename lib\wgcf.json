{"log": {"loglevel": "debug"}, "dns": {"disableFallbackIfMatch": true, "hosts": {}, "queryStrategy": "UseIP", "servers": [{"address": "tcp://*******", "concurrency": true}, {"address": "tcp+local://*********:53", "concurrency": true, "domains": ["full:cdn-all.xn--b6gac.eu.org"], "skipFallback": true}]}, "outbounds": [{"settings": {"secretKey": "KEY", "mtu": 1400, "peers": [{"publicKey": "bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=", "endpoint": "engage.cloudflareclient.com:2408", "keepAlive": 30}], "address": ["**********/32", "2606:4700:110:893c:845c:536b:5565:8106/128"], "kernelMode": false, "worker": 16}, "protocol": "wireguard", "streamSettings": {"network": "tcp"}, "tag": "directwarp"}, {"settings": {"secretKey": "KEY", "mtu": 1280, "peers": [{"publicKey": "bmXOC+F1FxEMF9dyiK2H5/1SUtzH0JuVo51h2wPfgyo=", "endpoint": "engage.cloudflareclient.com:2408", "keepAlive": 30}], "address": ["**********/32", "2606:4700:110:893c:845c:536b:5565:8106/128"], "kernelMode": false, "worker": 16}, "protocol": "wireguard", "streamSettings": {"network": "tcp"}, "tag": "warpoverwarp", "proxySettings": {"tag": "directwarp", "transportLayer": true}}, {"protocol": "vless", "settings": {"vnext": [{"address": "cdn-all.xn--b6gac.eu.org", "port": 443, "users": [{"id": "VLESSID", "encryption": "none", "level": 0}]}]}, "streamSettings": {"network": "ws", "security": "tls", "tlsSettings": {"serverName": "VLESSCFWORKERNAME", "allowInsecure": true}, "wsSettings": {"headers": {"Host": "VLESSCFWORKERNAME"}, "path": "/?ed=2048"}}, "tag": "vlesscf", "proxySettings": {"tag": "directfragment", "transportLayer": true}}, {"protocol": "vless", "settings": {"vnext": [{"address": "cdn-all.xn--b6gac.eu.org", "port": 443, "users": [{"id": "VLESSID", "encryption": "none", "level": 0}]}]}, "streamSettings": {"network": "ws", "security": "tls", "tlsSettings": {"serverName": "VLESSCFWORKERNAME", "allowInsecure": true}, "wsSettings": {"headers": {"Host": "VLESSCFWORKERNAME"}, "path": "/?ed=2048"}}, "tag": "vlesscfoverwarp", "proxySettings": {"tag": "directwarp", "transportLayer": true}}, {"protocol": "freedom", "tag": "direct"}, {"protocol": "freedom", "settings": {"fragment": {"packets": "tlshello", "length": "40-60", "interval": "30-50"}}, "tag": "directfragment"}], "inbounds": [{"listen": "0.0.0.0", "port": 10070, "protocol": "http", "settings": {"allowTransparent": true}, "tag": "http"}, {"port": 10071, "protocol": "socks", "settings": {"udp": true, "auth": "<PERSON><PERSON><PERSON>"}, "tag": "socks5"}, {"port": 10072, "protocol": "socks", "settings": {"udp": true, "auth": "<PERSON><PERSON><PERSON>"}, "tag": "socks5overvless"}, {"port": 10073, "protocol": "socks", "settings": {"udp": true, "auth": "<PERSON><PERSON><PERSON>"}, "tag": "socks5overwarpoverwarp"}, {"port": 10074, "protocol": "socks", "settings": {"udp": true, "auth": "<PERSON><PERSON><PERSON>"}, "tag": "socks5overvlessoverwarp"}], "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "ip": ["127.0.0.1"], "outboundTag": "direct"}, {"type": "field", "inboundTag": ["socks5"], "outboundTag": "directwarp"}, {"type": "field", "inboundTag": ["socks5overvless"], "outboundTag": "vlesscf"}, {"type": "field", "inboundTag": ["http"], "outboundTag": "vlesscf"}, {"type": "field", "inboundTag": ["socks5overwarpoverwarp"], "outboundTag": "warpoverwarp"}, {"type": "field", "inboundTag": ["socks5overvlessoverwarp"], "outboundTag": "vlesscfoverwarp"}]}}