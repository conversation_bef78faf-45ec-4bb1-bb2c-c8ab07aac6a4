# -*- coding: utf-8 -*-
# by @嗷呜
import sys
import re
import json
import time
import hashlib
import base64
from urllib.parse import urljoin, unquote
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from base.spider import Spider
try:
    from pyquery import PyQuery as pq
except ImportError:
    print("PyQuery not installed, using basic HTML parsing")


class Spider(Spider):

    def init(self, extend=""):
        self.host = 'https://hbottv.com'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 11; M2012K10C Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36',
            'Referer': self.host,
            'Origin': self.host
        }
        pass

    def getName(self):
        return "HBOTTV"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    def homeContent(self, filter):
        result = {}
        classes = [
            {'type_name': '电影', 'type_id': '1'},
            {'type_name': '电视剧', 'type_id': '2'},
            {'type_name': '综艺', 'type_id': '23'},
            {'type_name': '动漫', 'type_id': '3'}
        ]
        result['class'] = classes
        return result

    def homeVideoContent(self):
        pass

    def categoryContent(self, tid, pg, filter, extend):
        try:
            # 生成时间戳和签名
            t = str(int(time.time()))
            key = self.md5("DS" + t + "DCC147D11943AF75")
            
            # 构建POST数据
            data = {
                'type': tid,
                'page': pg,
                'time': t,
                'key': key
            }
            
            # 发送POST请求
            url = f'{self.host}/index.php/api/vod'
            response = self.post(url, data=data, headers=self.headers)
            json_data = response.json()
            
            videos = []
            if 'list' in json_data and json_data['list']:
                for item in json_data['list']:
                    # 处理图片URL
                    pic_url = item.get('vod_pic', '')
                    if pic_url and not pic_url.startswith('http'):
                        pic_url = urljoin(self.host, pic_url)
                    
                    videos.append({
                        'vod_id': item.get('vod_id', ''),
                        'vod_name': item.get('vod_name', ''),
                        'vod_pic': pic_url,
                        'vod_remarks': item.get('vod_remarks', '')
                    })
            
            result = {
                'list': videos,
                'page': pg,
                'pagecount': 9999,
                'limit': 90,
                'total': 999999
            }
            return result
            
        except Exception as e:
            self.log(f"分类内容获取失败: {str(e)}")
            return {'list': [], 'page': pg, 'pagecount': 1, 'limit': 90, 'total': 0}

    def detailContent(self, ids):
        try:
            vid = ids[0]
            url = f'{self.host}/detail/{vid}.html'
            response = self.fetch(url, headers=self.headers)
            doc = pq(response.text)
            
            # 解析基本信息
            title = doc('.slide-info-title').text().strip()
            img = doc('.detail-pic .lazy').attr('data-src') or doc('.detail-pic img').attr('src')
            if img and not img.startswith('http'):
                img = urljoin(self.host, img)
            
            # 解析描述信息
            remarks = doc('.slide-info-remarks').eq(0).text().strip()
            year = doc('.slide-info-remarks').eq(1).text().strip()
            area = doc('.slide-info-remarks').eq(2).text().strip()
            director = doc('.slide-info').eq(1).find('strong').text().strip()
            actor = doc('.info-parameter ul li').eq(3).text().strip()
            content = doc('#height_limit').text().strip()
            
            # 解析播放列表
            play_from_list = []
            play_url_list = []
            
            # 获取播放源标签
            tabs = doc('.anthology.wow.fadeInUp.animated .swiper-wrapper a')
            for i, tab in enumerate(tabs.items()):
                tab_name = tab.find('span').text().strip() or f"播放源{i+1}"
                play_from_list.append(tab_name)
                
                # 获取对应的播放列表
                episodes = doc(f'.anthology-list-box').eq(i).find('li')
                episode_list = []
                for ep in episodes.items():
                    ep_title = ep.text().strip()
                    ep_url = ep.find('a').attr('href')
                    if ep_url:
                        episode_list.append(f"{ep_title}${ep_url}")
                
                play_url_list.append('#'.join(episode_list))
            
            vod = {
                'vod_name': title,
                'vod_pic': img,
                'vod_year': year,
                'vod_area': area,
                'vod_director': director,
                'vod_actor': actor,
                'vod_remarks': remarks,
                'vod_content': content,
                'vod_play_from': '$$$'.join(play_from_list),
                'vod_play_url': '$$$'.join(play_url_list)
            }
            
            return {'list': [vod]}
            
        except Exception as e:
            self.log(f"详情页解析失败: {str(e)}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        try:
            url = f'{self.host}/index.php/ajax/suggest'
            params = {
                'mid': '1',
                'wd': key,
                'limit': '50'
            }
            
            response = self.fetch(url, params=params, headers=self.headers)
            json_data = response.json()
            
            videos = []
            if 'list' in json_data and json_data['list']:
                for item in json_data['list']:
                    pic_url = item.get('pic', '')
                    if pic_url and not pic_url.startswith('http'):
                        pic_url = urljoin(self.host, pic_url)
                    
                    videos.append({
                        'vod_id': item.get('id', ''),
                        'vod_name': item.get('name', ''),
                        'vod_pic': pic_url,
                        'vod_remarks': ''
                    })
            
            return {
                'list': videos,
                'page': pg,
                'pagecount': 1,
                'limit': 50,
                'total': len(videos)
            }
            
        except Exception as e:
            self.log(f"搜索失败: {str(e)}")
            return {'list': [], 'page': pg}

    def playerContent(self, flag, id, vipFlags):
        try:
            # 请求播放页面
            response = self.fetch(id, headers=self.headers)
            html = response.text
            
            # 解析JavaScript中的播放器配置
            player_reg = r'var (player_\w+)\s*=\s*({[\s\S]*?})\s*</script>'
            match = re.search(player_reg, html)
            
            if match:
                var_name = match.group(1)
                hconf = match.group(2)
                
                try:
                    # 简单的JSON解析（处理JavaScript对象）
                    hconf = re.sub(r'(\w+):', r'"\1":', hconf)  # 给键加引号
                    hconf = re.sub(r"'", '"', hconf)  # 单引号转双引号
                    json_data = json.loads(hconf)
                    
                    url = json_data.get('url', '')
                    encrypt = json_data.get('encrypt', '0')
                    
                    # 处理加密
                    if encrypt == '1':
                        url = unquote(url)
                    elif encrypt == '2':
                        try:
                            url = unquote(base64.b64decode(url).decode('utf-8'))
                        except:
                            url = unquote(url)
                    
                    # 判断是否为直链
                    if re.search(r'\.(m3u8|mp4|m4a|mp3)', url):
                        return {
                            'parse': 0,
                            'url': url,
                            'header': self.headers
                        }
                    else:
                        return {
                            'parse': 1,
                            'url': url,
                            'header': self.headers
                        }
                        
                except json.JSONDecodeError:
                    self.log("JSON解析失败")
                    
            return {
                'parse': 1,
                'url': id,
                'header': self.headers
            }
            
        except Exception as e:
            self.log(f"播放链接解析失败: {str(e)}")
            return {
                'parse': 1,
                'url': id,
                'header': self.headers
            }

    def localProxy(self, param):
        pass

    def md5(self, text):
        """MD5加密"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()


if __name__ == "__main__":
    sp = Spider()
    sp.init()

    # 测试首页内容
    print("=== 测试首页内容 ===")
    result = sp.homeContent(False)
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 测试分类内容
    print("\n=== 测试分类内容 ===")
    result = sp.categoryContent('1', '1', False, {})
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 测试搜索功能
    print("\n=== 测试搜索功能 ===")
    result = sp.searchContent('战狼', False, '1')
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 测试详情页（使用第一个搜索结果的ID）
    if result['list']:
        print("\n=== 测试详情页 ===")
        detail_result = sp.detailContent([result['list'][0]['vod_id']])
        print(json.dumps(detail_result, ensure_ascii=False, indent=2))
