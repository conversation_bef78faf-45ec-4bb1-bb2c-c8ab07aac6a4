# -*- coding: utf-8 -*-
# HDmoli影视爬虫插件
# 目标网站：https://www.hdmoli.pro/
# 开发者：AI Assistant
# 基于PyramidStore框架

import sys
import re
import json
from urllib.parse import urljoin, quote
sys.path.append('..')
from base.spider import Spider


class Spider(Spider):
    
    def init(self, extend=""):
        """初始化爬虫配置"""
        self.host = "https://www.hdmoli.pro"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        pass

    def getName(self):
        return "HDmoli影视"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    def homeContent(self, filter):
        """获取首页分类数据"""
        result = {}
        
        # 定义分类
        classes = [
            {'type_name': '电影', 'type_id': '1'},
            {'type_name': '剧集', 'type_id': '2'},
            {'type_name': '动画', 'type_id': '41'}
        ]
        
        # 定义筛选条件
        filters = {
            '1': [  # 电影筛选
                {
                    'key': 'class',
                    'name': '类型',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '动作', 'v': '5'},
                        {'n': '爱情', 'v': '6'},
                        {'n': '科幻', 'v': '7'},
                        {'n': '恐怖', 'v': '8'},
                        {'n': '战争', 'v': '9'},
                        {'n': '喜剧', 'v': '10'},
                        {'n': '纪录片', 'v': '11'},
                        {'n': '剧情', 'v': '12'},
                        {'n': '犯罪', 'v': '30'},
                        {'n': '动画', 'v': '32'}
                    ]
                },
                {
                    'key': 'area',
                    'name': '地区',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '美国', 'v': '美国'},
                        {'n': '韩国', 'v': '韩国'},
                        {'n': '英国', 'v': '英国'},
                        {'n': '日本', 'v': '日本'},
                        {'n': '泰国', 'v': '泰国'},
                        {'n': '中国', 'v': '中国'},
                        {'n': '其他', 'v': '其他'}
                    ]
                },
                {
                    'key': 'year',
                    'name': '年份',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '2025', 'v': '2025'},
                        {'n': '2024', 'v': '2024'},
                        {'n': '2023', 'v': '2023'},
                        {'n': '2022', 'v': '2022'},
                        {'n': '2021', 'v': '2021'},
                        {'n': '2020', 'v': '2020'},
                        {'n': '更早', 'v': 'more'}
                    ]
                },
                {
                    'key': 'by',
                    'name': '排序',
                    'value': [
                        {'n': '时间', 'v': 'time'},
                        {'n': '评分', 'v': 'douban'}
                    ]
                }
            ],
            '2': [  # 剧集筛选（同电影）
                {
                    'key': 'class',
                    'name': '类型',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '动作', 'v': '5'},
                        {'n': '爱情', 'v': '6'},
                        {'n': '科幻', 'v': '7'},
                        {'n': '恐怖', 'v': '8'},
                        {'n': '战争', 'v': '9'},
                        {'n': '喜剧', 'v': '10'},
                        {'n': '纪录片', 'v': '11'},
                        {'n': '剧情', 'v': '12'},
                        {'n': '犯罪', 'v': '30'}
                    ]
                },
                {
                    'key': 'area',
                    'name': '地区',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '美国', 'v': '美国'},
                        {'n': '韩国', 'v': '韩国'},
                        {'n': '英国', 'v': '英国'},
                        {'n': '日本', 'v': '日本'},
                        {'n': '中国', 'v': '中国'},
                        {'n': '其他', 'v': '其他'}
                    ]
                },
                {
                    'key': 'year',
                    'name': '年份',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '2025', 'v': '2025'},
                        {'n': '2024', 'v': '2024'},
                        {'n': '2023', 'v': '2023'},
                        {'n': '2022', 'v': '2022'},
                        {'n': '2021', 'v': '2021'},
                        {'n': '2020', 'v': '2020'},
                        {'n': '更早', 'v': 'more'}
                    ]
                },
                {
                    'key': 'by',
                    'name': '排序',
                    'value': [
                        {'n': '时间', 'v': 'time'},
                        {'n': '评分', 'v': 'douban'}
                    ]
                }
            ],
            '41': [  # 动画筛选（简化版）
                {
                    'key': 'area',
                    'name': '地区',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '日本', 'v': '日本'},
                        {'n': '美国', 'v': '美国'},
                        {'n': '中国', 'v': '中国'},
                        {'n': '其他', 'v': '其他'}
                    ]
                },
                {
                    'key': 'year',
                    'name': '年份',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '2025', 'v': '2025'},
                        {'n': '2024', 'v': '2024'},
                        {'n': '2023', 'v': '2023'},
                        {'n': '2022', 'v': '2022'},
                        {'n': '2021', 'v': '2021'},
                        {'n': '2020', 'v': '2020'}
                    ]
                }
            ]
        }
        
        result['class'] = classes
        result['filters'] = filters
        return result

    def homeVideoContent(self):
        """获取推荐视频内容"""
        try:
            url = f"{self.host}/"
            response = self.fetch(url, headers=self.headers)
            html = self.html(response.text)

            videos = []
            seen_ids = set()  # 用于去重

            # 更精确的XPath选择器 - 只选择主要内容区域的视频链接
            # 避免导航栏和其他重复链接
            sections = ['最近更新', '电影', '剧集', '动画']

            for section in sections:
                # 查找每个分区的视频链接
                section_xpath = f'//h3[contains(text(), "{section}")]/following-sibling::*//a[contains(@href, "/movie/index") and @title]'
                items = html.xpath(section_xpath)

                for item in items:
                    try:
                        title = item.get('title', '').strip()
                        href = item.get('href', '')

                        if title and href:
                            # 提取ID
                            id_match = re.search(r'/movie/index(\d+)\.html', href)
                            if id_match:
                                vod_id = id_match.group(1)

                                # 去重检查
                                if vod_id in seen_ids:
                                    continue
                                seen_ids.add(vod_id)

                                # 获取评分信息 - 从前面的文本中提取
                                remarks = ''
                                prev_text = item.xpath('./preceding-sibling::text()[1]')
                                if prev_text:
                                    text = prev_text[0].strip()
                                    if '分' in text and any(c.isdigit() for c in text):
                                        remarks = text

                                # 如果没有找到评分，尝试从父级元素查找
                                if not remarks:
                                    parent = item.getparent()
                                    if parent is not None:
                                        all_text = parent.xpath('.//text()')
                                        for text in all_text:
                                            if '分' in text and any(c.isdigit() for c in text):
                                                remarks = text.strip()
                                                break

                                videos.append({
                                    'vod_id': vod_id,
                                    'vod_name': title,
                                    'vod_pic': '',  # 首页不获取图片，减少复杂度
                                    'vod_remarks': remarks
                                })

                                # 限制推荐数量
                                if len(videos) >= 12:
                                    break

                    except Exception as e:
                        self.log(f"解析推荐视频项目失败: {e}")
                        continue

                if len(videos) >= 12:
                    break

            return {'list': videos}

        except Exception as e:
            self.log(f"获取推荐视频失败: {e}")
            return {'list': []}

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类内容列表"""
        try:
            result = {}
            videos = []

            # 构建URL
            if extend and any(extend.values()):
                # 使用筛选搜索
                url = f"{self.host}/search.php"
                params = {
                    'searchtype': '5',
                    'tid': tid,
                    'page': pg
                }

                # 添加筛选参数
                if extend.get('class'):
                    if extend['class'].isdigit():
                        # 如果是数字，使用子分类URL
                        url = f"{self.host}/mlist/index{extend['class']}.html"
                        if int(pg) > 1:
                            url = f"{self.host}/mlist/index{extend['class']}-{pg}.html"
                        params = {}
                    else:
                        params['jq'] = extend['class']

                if extend.get('area'):
                    params['area'] = extend['area']
                if extend.get('year'):
                    params['year'] = extend['year']
                if extend.get('by'):
                    params['order'] = extend['by']

                if params:
                    response = self.fetch(url, params=params, headers=self.headers)
                else:
                    response = self.fetch(url, headers=self.headers)
            else:
                # 使用默认分类URL
                if int(pg) == 1:
                    url = f"{self.host}/mlist/index{tid}.html"
                else:
                    url = f"{self.host}/mlist/index{tid}-{pg}.html"
                response = self.fetch(url, headers=self.headers)

            html = self.html(response.text)

            # 解析视频列表 - 使用更精确的选择器避免重复
            seen_ids = set()  # 用于去重

            # 方法1：尝试从列表页面的特定结构中提取
            # 查找包含视频信息的主要容器
            video_containers = html.xpath('//li[.//a[contains(@href, "/movie/index")]]')

            if not video_containers:
                # 方法2：如果没有找到li容器，直接查找链接
                items = html.xpath('//a[contains(@href, "/movie/index") and @title]')
                # 去重：只保留第一次出现的链接
                unique_items = []
                seen_hrefs = set()
                for item in items:
                    href = item.get('href', '')
                    if href not in seen_hrefs:
                        seen_hrefs.add(href)
                        unique_items.append(item)
                items = unique_items
            else:
                # 从容器中提取唯一的链接
                items = []
                for container in video_containers:
                    links = container.xpath('.//a[contains(@href, "/movie/index") and @title]')
                    if links:
                        items.append(links[0])  # 只取第一个链接

            for item in items:
                try:
                    title = item.get('title', '').strip()
                    href = item.get('href', '')

                    if title and href:
                        # 提取ID
                        id_match = re.search(r'/movie/index(\d+)\.html', href)
                        if id_match:
                            vod_id = id_match.group(1)

                            # 去重检查
                            if vod_id in seen_ids:
                                continue
                            seen_ids.add(vod_id)

                            # 获取评分和备注信息
                            remarks = ''
                            # 查找评分信息 - 从前面的文本中提取
                            prev_text = item.xpath('./preceding-sibling::text()[1]')
                            if prev_text:
                                text = prev_text[0].strip()
                                if '分' in text and any(c.isdigit() for c in text):
                                    remarks = text

                            # 如果没有找到评分，尝试从父级元素查找
                            if not remarks:
                                parent = item.getparent()
                                if parent is not None:
                                    all_text = parent.xpath('.//text()')
                                    for text in all_text:
                                        if '分' in text and any(c.isdigit() for c in text):
                                            remarks = text.strip()
                                            break

                            videos.append({
                                'vod_id': vod_id,
                                'vod_name': title,
                                'vod_pic': '',  # 分类页面不获取图片，提高性能
                                'vod_remarks': remarks
                            })
                except Exception as e:
                    self.log(f"解析分类视频项目失败: {e}")
                    continue

            result['list'] = videos
            result['page'] = pg
            result['pagecount'] = 9999  # 设置较大值，实际翻页由网站控制
            result['limit'] = 20
            result['total'] = 999999

            return result

        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return {
                'list': [],
                'page': pg,
                'pagecount': 1,
                'limit': 20,
                'total': 0
            }

    def detailContent(self, ids):
        """获取详情页面数据"""
        try:
            vod_id = ids[0]
            url = f"{self.host}/movie/index{vod_id}.html"
            response = self.fetch(url, headers=self.headers)
            html = self.html(response.text)

            # 提取基本信息 - 修正标题提取
            title = ''
            # 方法1：从h1标签提取
            title_elem = html.xpath('//h1/text()')
            if title_elem:
                title = title_elem[0].strip()
                # 清理标题，移除网站名称
                if ' - HDmoli' in title:
                    title = title.replace(' - HDmoli', '')
                if ' 完结' in title:
                    title = title.replace(' 完结', '')

            # 方法2：如果h1没有，从title标签提取
            if not title:
                title_elem = html.xpath('//title/text()')
                if title_elem:
                    title = title_elem[0].strip()
                    if ' - HDmoli' in title:
                        title = title.replace(' - HDmoli', '')

            # 提取封面图片 - 查找详情页面的主图片
            pic = ''
            # 查找详情页面的主要图片
            pic_elem = html.xpath('//img[contains(@src, "img20.360buyimg.com") or contains(@src, "/pic/")]/@src')
            if pic_elem:
                pic = pic_elem[0]
                if not pic.startswith('http'):
                    pic = urljoin(self.host, pic)
            else:
                # 备用方案：查找其他图片
                pic_elem = html.xpath('//img/@src')
                for p in pic_elem:
                    if 'logo' not in p.lower() and 'icon' not in p.lower() and 'close' not in p.lower():
                        pic = urljoin(self.host, p)
                        break

            # 提取评分
            score_elem = html.xpath('//span[@class="branch"]/text()')
            score = ''
            for elem in score_elem:
                if '豆瓣评分' in elem:
                    score_match = re.search(r'(\d+\.?\d*)', elem)
                    if score_match:
                        score = score_match.group(1)
                        break

            # 提取详细信息 - 从页面文本中解析
            page_text = response.text

            # 解析分类、地区、年份等信息
            type_name = ''
            area = ''
            year = ''
            actor = ''
            director = ''

            # 从页面文本中提取信息 - 使用更精确的正则表达式
            # 分类信息
            type_match = re.search(r'分类：([^地区\n]+?)(?:\s+地区：|$)', page_text)
            if type_match:
                type_name = type_match.group(1).strip()
                # 清理HTML标签
                type_name = re.sub(r'<[^>]+>', '', type_name)

            # 地区信息
            area_match = re.search(r'地区：([^年份\n]+?)(?:\s+年份：|$)', page_text)
            if area_match:
                area = area_match.group(1).strip()
                area = re.sub(r'<[^>]+>', '', area)

            # 年份信息
            year_match = re.search(r'年份：(\d{4})', page_text)
            if year_match:
                year = year_match.group(1)

            # 演员信息
            actor_match = re.search(r'演员：([^导演\n]+?)(?:\s+导演：|$)', page_text)
            if actor_match:
                actor = actor_match.group(1).strip()
                # 清理链接标签，只保留演员名字
                actor = re.sub(r'<a[^>]*>([^<]+)</a>', r'\1', actor)
                actor = re.sub(r'<[^>]+>', '', actor)

            # 导演信息
            director_match = re.search(r'导演：([^更新\n]+?)(?:\s+更新：|$)', page_text)
            if director_match:
                director = director_match.group(1).strip()
                director = re.sub(r'<a[^>]*>([^<]+)</a>', r'\1', director)
                director = re.sub(r'<[^>]+>', '', director)

            # 提取简介
            desc = ''
            desc_match = re.search(r'\*\*简介：\*\* ([^*]+?)(?:\*\*|$)', page_text)
            if desc_match:
                desc = desc_match.group(1).strip()
            else:
                # 备用方案
                desc_match = re.search(r'简介：([^短评\n]+)', page_text)
                if desc_match:
                    desc = desc_match.group(1).strip()

            # 提取播放源和播放链接 - 正确分离不同播放源
            play_from = []
            play_url = []

            # 查找播放源标签 - 从详情页面的播放源标题中提取
            source_headers = html.xpath('//h3[contains(text(), "在线播放")]/following-sibling::*//text()[contains(., "线路") or contains(., "海外") or contains(., "高清")]')

            # 方法1：解析在线播放链接（按播放源分组）
            play_sections = html.xpath('//h3[contains(text(), "在线播放")]/following-sibling::*')

            current_source = ""
            episodes = []

            for section in play_sections:
                # 查找播放源标识
                source_text = section.xpath('.//text()[contains(., "线路") or contains(., "海外") or contains(., "高清")]')
                if source_text:
                    # 如果有之前的播放源，先保存
                    if current_source and episodes:
                        play_from.append(current_source)
                        play_url.append('#'.join(episodes))
                        episodes = []

                    # 设置新的播放源
                    current_source = source_text[0].strip()
                    if '[' in current_source and ']' in current_source:
                        current_source = current_source.split('[')[1].split(']')[0]

                # 查找该播放源下的播放链接
                play_links = section.xpath('.//a[contains(@href, "/play/")]')
                for link in play_links:
                    ep_title = link.text.strip() if link.text else '播放'
                    ep_url = link.get('href', '')
                    if ep_url:
                        episodes.append(f"{ep_title}${ep_url}")

            # 保存最后一个播放源
            if current_source and episodes:
                play_from.append(current_source)
                play_url.append('#'.join(episodes))

            # 方法2：查找网盘下载链接
            download_section = html.xpath('//h3[contains(text(), "视频下载")]/following-sibling::*')

            # 夸克网盘
            quark_links = re.findall(r'https://pan\.quark\.cn/s/[a-zA-Z0-9]+', page_text)
            if quark_links:
                play_from.append("夸克网盘")
                episodes = []
                for i, link in enumerate(quark_links):
                    ep_title = f"夸克网盘{i+1}" if len(quark_links) > 1 else "夸克网盘"
                    episodes.append(f"{ep_title}${link}")
                play_url.append('#'.join(episodes))

            # UC网盘
            uc_links = re.findall(r'https://drive\.uc\.cn/s/[a-zA-Z0-9]+', page_text)
            if uc_links:
                play_from.append("UC网盘")
                episodes = []
                for i, link in enumerate(uc_links):
                    ep_title = f"UC网盘{i+1}" if len(uc_links) > 1 else "UC网盘"
                    episodes.append(f"{ep_title}${link}")
                play_url.append('#'.join(episodes))

            # 百度网盘
            baidu_links = re.findall(r'https://pan\.baidu\.com/s/[a-zA-Z0-9]+', page_text)
            if baidu_links:
                play_from.append("百度网盘")
                episodes = []
                for i, link in enumerate(baidu_links):
                    ep_title = f"百度网盘{i+1}" if len(baidu_links) > 1 else "百度网盘"
                    episodes.append(f"{ep_title}${link}")
                play_url.append('#'.join(episodes))

            # 如果没有找到任何播放源，创建一个默认的
            if not play_from:
                play_from.append("暂无播放源")
                play_url.append("暂无播放链接$")

            vod = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'type_name': type_name,
                'vod_year': year,
                'vod_area': area,
                'vod_actor': actor,
                'vod_director': director,
                'vod_content': desc,
                'vod_play_from': '$$$'.join(play_from),
                'vod_play_url': '$$$'.join(play_url)
            }

            return {'list': [vod]}

        except Exception as e:
            self.log(f"获取详情失败: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        """搜索功能"""
        try:
            url = f"{self.host}/search.php"
            params = {
                'searchkey': key,
                'page': pg
            }

            response = self.fetch(url, params=params, headers=self.headers)
            html = self.html(response.text)

            videos = []

            # 解析搜索结果 - 使用更精确的选择器
            seen_ids = set()  # 用于去重

            # 搜索结果页面的结构不同，需要特殊处理
            # 查找搜索结果的主要容器
            result_items = html.xpath('//li[.//a[contains(@href, "/movie/index")]]')

            if not result_items:
                # 如果没有找到li容器，直接查找链接（去重）
                items = html.xpath('//a[contains(@href, "/movie/index") and @title]')
                # 去重：只保留第一次出现的链接
                unique_items = []
                seen_hrefs = set()
                for item in items:
                    href = item.get('href', '')
                    if href not in seen_hrefs:
                        seen_hrefs.add(href)
                        unique_items.append(item)
                items = unique_items
            else:
                # 从容器中提取唯一的链接
                items = []
                for container in result_items:
                    links = container.xpath('.//a[contains(@href, "/movie/index") and @title]')
                    if links:
                        items.append(links[0])  # 只取第一个链接

            for item in items:
                try:
                    title = item.get('title', '').strip()
                    href = item.get('href', '')

                    if title and href:
                        # 提取ID
                        id_match = re.search(r'/movie/index(\d+)\.html', href)
                        if id_match:
                            vod_id = id_match.group(1)

                            # 去重检查
                            if vod_id in seen_ids:
                                continue
                            seen_ids.add(vod_id)

                            # 获取评分和备注信息
                            remarks = ''
                            # 查找评分信息 - 从前面的文本中提取
                            prev_text = item.xpath('./preceding-sibling::text()[1]')
                            if prev_text:
                                text = prev_text[0].strip()
                                if '分' in text and any(c.isdigit() for c in text):
                                    remarks = text

                            # 如果没有找到评分，尝试从父级元素查找
                            if not remarks:
                                parent = item.getparent()
                                if parent is not None:
                                    all_text = parent.xpath('.//text()')
                                    for text in all_text:
                                        if '分' in text and any(c.isdigit() for c in text):
                                            remarks = text.strip()
                                            break

                            videos.append({
                                'vod_id': vod_id,
                                'vod_name': title,
                                'vod_pic': '',  # 搜索页面不获取图片
                                'vod_remarks': remarks
                            })
                except Exception as e:
                    self.log(f"解析搜索结果项目失败: {e}")
                    continue

            result = {
                'list': videos,
                'page': pg,
                'pagecount': 9999,
                'limit': 20,
                'total': 999999
            }

            return result

        except Exception as e:
            self.log(f"搜索失败: {e}")
            return {
                'list': [],
                'page': pg,
                'pagecount': 1,
                'limit': 20,
                'total': 0
            }

    def playerContent(self, flag, id, vipFlags):
        """播放链接解析"""
        try:
            # 处理播放页面URL
            if id.startswith('/play/'):
                play_url = urljoin(self.host, id)
            else:
                play_url = f"{self.host}/play/{id}"

            response = self.fetch(play_url, headers=self.headers)
            html = self.html(response.text)

            # 查找网盘链接
            quark_links = html.xpath('//a[contains(@href, "pan.quark.cn")]/@href')

            if quark_links:
                # 返回夸克网盘链接
                return {
                    'parse': 0,
                    'url': quark_links[0],
                    'header': self.headers
                }

            # 如果没有找到网盘链接，查找其他播放链接
            video_links = html.xpath('//video/@src | //source/@src')
            if video_links:
                return {
                    'parse': 0,
                    'url': urljoin(self.host, video_links[0]),
                    'header': self.headers
                }

            # 如果都没有找到，返回播放页面让用户手动处理
            return {
                'parse': 0,
                'url': play_url,
                'header': self.headers
            }

        except Exception as e:
            self.log(f"播放链接解析失败: {e}")
            return {
                'parse': 0,
                'url': '',
                'header': self.headers
            }

    def localProxy(self, param):
        """本地代理处理"""
        pass
