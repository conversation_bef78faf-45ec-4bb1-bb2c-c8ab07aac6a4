# -*- coding: utf-8 -*-
# 聚墨影视爬虫 - 适用于PyramidStore影视搜索系统
# 网站: https://www.jumomo.cc/
# 
# 说明:
# 1. 本爬虫支持首页内容、分类内容、搜索内容和详情内容的获取
# 2. 播放链接为加密链接，设置为需要解析模式，由播放器自行处理
# 3. 支持的分类: 电影、剧集、综艺、动漫
#
# 使用方法:
# - 首页内容: homeContent(False)
# - 分类内容: categoryContent('1', '1', False, {})  # 电影第1页
# - 搜索内容: searchContent('关键词', False)
# - 详情内容: detailContent(['视频ID'])
# - 播放内容: playerContent('', '播放链接', {})
#

import sys
import json
import re
import time
from urllib.parse import urljoin, quote
sys.path.append("..")
from base.spider import Spider

class Spider(Spider):

    def init(self, extend=""):
        self.host = 'https://www.jumomo.cc'
        print(f"Initialized 聚墨影视 spider with host: {self.host}")
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': self.host,
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }

    def getName(self):
        return "聚墨影视"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    def homeContent(self, filter):
        result = {}
        classes = []
        
        # 主要分类
        classes.append({"type_id": "1", "type_name": "电影"})
        classes.append({"type_id": "2", "type_name": "剧集"})
        classes.append({"type_id": "3", "type_name": "综艺"})
        classes.append({"type_id": "4", "type_name": "动漫"})
        
        result['class'] = classes
        
        # 获取首页推荐内容
        html = self.fetch(self.host, headers=self.headers).text
        videos = self.parseVideoList(html)
        result['list'] = videos
        
        return result

    def homeVideoContent(self):
        html = self.fetch(self.host, headers=self.headers).text
        videos = self.parseVideoList(html)
        return {'list': videos}

    def categoryContent(self, tid, pg, filter, extend):
        result = {}
        url = f'{self.host}/vodshow/{tid}--------{pg}---.html'
        
        html = self.fetch(url, headers=self.headers).text
        videos = self.parseVideoList(html)
        
        result['list'] = videos
        result['page'] = pg
        result['pagecount'] = self.getPageCount(html) or 9999
        result['limit'] = 30
        result['total'] = 999999
        
        return result

    def detailContent(self, ids):
        url = f'{self.host}/voddetail/{ids[0]}.html'
        html = self.fetch(url, headers=self.headers).text
        
        # 解析视频详情
        vod = {}
        vod['vod_id'] = ids[0]
        
        # 从网页标题提取视频标题
        title_match = re.search(r'<title>([^<]*?) - ', html)
        if title_match:
            title = title_match.group(1).strip()
            # 移除"详情介绍"等后缀
            title = re.sub(r'详情介绍.*$', '', title)
            vod['vod_name'] = title
        
        # 提取图片URL - 添加正确的图片提取
        pic_match = re.search(r'<div class="stui-content__thumb">.*?<img [^>]*?data-original="([^"]+)"', html, re.DOTALL)
        if not pic_match:
            pic_match = re.search(r'<div class="stui-content__thumb">.*?<img [^>]*?src="([^"]+)"', html, re.DOTALL)
        if pic_match:
            pic_url = pic_match.group(1)
            if not pic_url.startswith('http'):
                pic_url = urljoin(self.host, pic_url)
            vod['vod_pic'] = pic_url
        
        # 提取类型、地区、年份 - 使用更精确的正则表达式
        type_match = re.search(r'类型：(.*?)(?=地区|年份|主演|导演)', html, re.DOTALL)
        if type_match:
            vod['type_name'] = self.cleanText(type_match.group(1))
        
        area_match = re.search(r'地区：\[([^\]]+)\]', html)
        if not area_match:
            area_match = re.search(r'地区：(.*?)(?=年份|主演|导演)', html, re.DOTALL)
        if area_match:
            vod['vod_area'] = self.cleanText(area_match.group(1))
        
        year_match = re.search(r'年份：\[([^\]]+)\]', html)
        if not year_match:
            year_match = re.search(r'年份：(.*?)(?=主演|导演)', html, re.DOTALL)
        if year_match:
            vod['vod_year'] = self.cleanText(year_match.group(1))
        
        # 提取演员和导演 - 使用更精确的正则表达式
        actor_match = re.search(r'主演：(.*?)(?=导演|更新|状态|简介)', html, re.DOTALL)
        if actor_match:
            vod['vod_actor'] = self.cleanText(actor_match.group(1))
        
        director_match = re.search(r'导演：(.*?)(?=更新|状态|简介)', html, re.DOTALL)
        if director_match:
            vod['vod_director'] = self.cleanText(director_match.group(1))
        
        # 尝试提取更新时间和状态
        update_match = re.search(r'更新：(.*?)(?=状态|简介)', html, re.DOTALL)
        if update_match:
            vod['vod_remarks'] = self.cleanText(update_match.group(1))
        
        status_match = re.search(r'状态：(.*?)(?=简介)', html, re.DOTALL)
        if status_match:
            status = self.cleanText(status_match.group(1))
            if 'vod_remarks' in vod:
                vod['vod_remarks'] += f" | {status}"
            else:
                vod['vod_remarks'] = status
        
        # 提取简介
        desc_match = re.search(r'剧情简介.*?</div>\s*<div[^>]*>(.*?)</div>', html, re.DOTALL)
        if desc_match:
            vod['vod_content'] = self.cleanText(desc_match.group(1))
        elif not desc_match:
            # 尝试其他方式提取简介
            desc_match = re.search(r'简介：(.*?)</div>', html, re.DOTALL)
            if desc_match:
                vod['vod_content'] = self.cleanText(desc_match.group(1))
        
        # 查找所有播放源
        play_sources = {}
        
        # 查找所有播放链接
        play_links = re.findall(r'<a[^>]*href="([^"]*?/vodplay/\d+-(\d+)-\d+\.html)"[^>]*>([^<]+)</a>', html)
        
        # 按播放源分组
        for url, source_id, name in play_links:
            if source_id not in play_sources:
                # 找出播放源的名称
                source_name_match = re.search(rf'<img src="/statics/icon/icon_\d+\.png"/>\s*<div[^>]*>([^<]*)</div>[^<]*<div[^>]*{source_id}', html, re.DOTALL)
                if not source_name_match:
                    # 尝试其他匹配方式
                    source_name_match = re.search(rf'<div[^>]*>([^<]*)</div>[^<]*<div[^>]*{source_id}', html, re.DOTALL)
                    
                source_name = source_name_match.group(1).strip() if source_name_match else f"播放源{source_id}"
                play_sources[source_id] = {"name": source_name, "episodes": []}
            
            play_sources[source_id]["episodes"].append(f"{name}${url}")
        
        # 构建播放源字符串
        source_names = []
        source_urls = []
        
        for source_id, source_data in play_sources.items():
            source_names.append(source_data["name"])
            source_urls.append("#".join(source_data["episodes"]))
        
        vod['vod_play_from'] = "$$$".join(source_names)
        vod['vod_play_url'] = "$$$".join(source_urls)
        
        result = {
            'list': [vod]
        }
        return result

    def searchContent(self, key, quick, pg="1"):
        url = f'{self.host}/vodsearch/{quote(key)}-------------.html'
        html = self.fetch(url, headers=self.headers).text
        
        videos = self.parseVideoList(html)
        return {'list': videos}

    def playerContent(self, flag, id, vipFlags):
        if not id.startswith(('http://', 'https://')):
            url = urljoin(self.host, id)
            html = self.fetch(url, headers=self.headers).text
            
            # 尝试提取player_aaaa变量
            player_data_match = re.search(r'player_aaaa\s*=\s*(\{.*?\})', html, re.DOTALL)
            if player_data_match:
                player_data_str = player_data_match.group(1)
                print(f"找到player_aaaa变量，提取关键信息")
                
                # 提取播放源信息
                from_match = re.search(r'"from":"([^"]+)"', player_data_str)
                if from_match:
                    from_value = from_match.group(1)
                    print(f"播放源: {from_value}")
                    
                    # 返回播放信息，设置为需要解析
                    return {
                        "parse": 1,
                        "url": url,
                        "flag": from_value,
                        "header": self.headers
                    }
            
            # 如果无法提取player_aaaa，尝试在页面中查找iframe
            iframe_match = re.search(r'<iframe[^>]*?src="([^"]+)"', html, re.DOTALL)
            if iframe_match:
                iframe_url = iframe_match.group(1)
                if not iframe_url.startswith('http'):
                    iframe_url = urljoin(self.host, iframe_url)
                
                print(f"找到iframe: {iframe_url}")
                return {"parse": 1, "url": iframe_url, "header": self.headers}
            
            # 如果都没找到，返回完整播放页面URL作为解析源
            print("使用完整页面URL进行解析")
            return {"parse": 1, "url": url, "header": self.headers}
        
        # 如果ID已经是完整URL，直接返回
        elif id.startswith(('http://', 'https://')):
            return {"parse": 0, "url": id, "header": self.headers}
        
        # 构造完整URL返回
        else:
            full_url = urljoin(self.host, id)
            return {"parse": 1, "url": full_url, "header": self.headers}

    def localProxy(self, param):
        pass
    
    def parseVideoList(self, html):
        videos = []
        
        # 先尝试匹配首页和分类页的视频列表
        pattern = r'<a[^>]*href="[^"]*?voddetail/(\d+)\.html"[^>]*?title="([^"]*?)"[^>]*?>.*?<img[^>]*?(?:data-original|src)="([^"]*?)".*?<div[^>]*?class="public-list-prb[^"]*?"[^>]*?>([^<]*?)</div>'
        
        for match in re.finditer(pattern, html, re.DOTALL):
            vod_id = match.group(1)
            vod_name = match.group(2)
            vod_pic = match.group(3)
            vod_remarks = match.group(4).strip()
            
            # 确保图片URL完整
            if not vod_pic.startswith('http'):
                vod_pic = urljoin(self.host, vod_pic)
            
            videos.append({
                'vod_id': vod_id,
                'vod_name': vod_name,
                'vod_pic': vod_pic,
                'vod_remarks': vod_remarks
            })
        
        # 如果上面的方法没有提取到视频，尝试另一种模式
        if not videos:
            # 查找所有包含voddetail的链接，这些通常是视频详情页的链接
            detail_links = re.findall(r'<a[^>]*?href="([^"]*?voddetail/(\d+)\.html)"[^>]*?title="([^"]*?)"', html)
            
            for link, vid, title in detail_links:
                # 对于每个找到的链接，尝试在其周围查找图片和备注
                img_match = re.search(r'<a[^>]*?href="[^"]*?' + vid + r'\.html".*?<img[^>]*?(?:data-original|src)="([^"]*?)"', html, re.DOTALL)
                vod_pic = img_match.group(1) if img_match else ""
                
                # 查找备注
                remark_match = re.search(r'<a[^>]*?href="[^"]*?' + vid + r'\.html".*?<div[^>]*?class="[^"]*?public-list-prb[^"]*?"[^>]*?>([^<]*?)</div>', html, re.DOTALL)
                vod_remarks = remark_match.group(1).strip() if remark_match else ""
                
                # 确保图片URL完整
                if vod_pic and not vod_pic.startswith('http'):
                    vod_pic = urljoin(self.host, vod_pic)
                
                videos.append({
                    'vod_id': vid,
                    'vod_name': title,
                    'vod_pic': vod_pic,
                    'vod_remarks': vod_remarks
                })
                
        # 搜索页面的视频列表格式可能不同，单独处理
        if "vodsearch" in html and not videos:
            search_pattern = r'<div[^>]*?class="anthology-list-box".*?href="[^"]*?voddetail/(\d+)\.html".*?title="([^"]*?)".*?<img[^>]*?(?:data-original|src)="([^"]*?)".*?<div[^>]*?class="public-list-prb[^"]*?"[^>]*?>([^<]*?)</div>'
            for match in re.finditer(search_pattern, html, re.DOTALL):
                vod_id = match.group(1)
                vod_name = match.group(2)
                vod_pic = match.group(3)
                vod_remarks = match.group(4).strip()
                
                # 确保图片URL完整
                if not vod_pic.startswith('http'):
                    vod_pic = urljoin(self.host, vod_pic)
                
                videos.append({
                    'vod_id': vod_id,
                    'vod_name': vod_name,
                    'vod_pic': vod_pic,
                    'vod_remarks': vod_remarks
                })
        
        return videos
    
    def getPageCount(self, html):
        # 尝试从分页信息中提取总页数
        match = re.search(r'>\s*(\d+)\s*</a>\s*<a[^>]*>下一页</a>', html)
        if match:
            return int(match.group(1))
        return None
    
    def cleanText(self, text):
        """清理HTML文本，移除标签和多余空白，处理特殊字符"""
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 处理特殊字符实体
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&quot;', '"')
        text = text.replace('&#39;', "'")
        
        # 移除方括号
        text = re.sub(r'\[|\]', '', text)
        
        # 合并多个空白字符为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        # 修复常见的错误格式
        text = re.sub(r'(\w),(\w)', r'\1, \2', text)  # 修复逗号后缺少空格的问题
        
        # 移除行首行尾空白
        return text.strip() 