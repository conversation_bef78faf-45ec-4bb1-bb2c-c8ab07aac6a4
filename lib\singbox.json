{"log": {"level": "debug", "timestamp": true}, "dns": {"servers": [{"tag": "remote", "address": "https://*******/dns-query", "strategy": "prefer_ipv4", "detour": "select"}, {"tag": "local", "address": "https://*********/dns-query", "strategy": "prefer_ipv4", "detour": "direct"}, {"tag": "block", "address": "rcode://success"}, {"tag": "fakeip", "address": "fakeip"}], "rules": [{"outbound": ["any"], "server": "local"}, {"clash_mode": "Global", "server": "remote"}, {"clash_mode": "Direct", "server": "local"}], "fakeip": {"enabled": true, "inet4_range": "**********/15", "inet6_range": "fc00::/18"}, "strategy": "prefer_ipv4", "independent_cache": true, "reverse_mapping": true}, "inbounds": [{"type": "mixed", "tag": "mixed-in", "listen": "0.0.0.0", "listen_port": 10172, "tcp_fast_open": true, "sniff": false, "sniff_override_destination": false, "domain_strategy": "prefer_ipv4", "set_system_proxy": false}, {"type": "socks", "tag": "socks-in", "listen": "0.0.0.0", "listen_port": 10173, "tcp_fast_open": true, "sniff": false, "sniff_override_destination": false, "domain_strategy": "prefer_ipv4"}, {"type": "mixed", "tag": "mixed-in2", "listen": "0.0.0.0", "listen_port": 10174, "tcp_fast_open": true, "sniff": false, "sniff_override_destination": false, "domain_strategy": "prefer_ipv4", "set_system_proxy": false}, {"type": "mixed", "tag": "mixed-in3", "listen": "0.0.0.0", "listen_port": 10175, "tcp_fast_open": true, "sniff": false, "sniff_override_destination": false, "domain_strategy": "prefer_ipv4", "set_system_proxy": false}], "outbounds": [{"type": "selector", "tag": "select", "outbounds": ["urltest"], "default": "urltest"}, {"type": "urltest", "tag": "urltest", "interval": "30m", "idle_timeout": "60m", "interrupt_exist_connections": false, "outbounds": null}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"type": "logical", "mode": "or", "rules": [{"protocol": "dns"}, {"port": 53}], "outbound": "dns-out"}, {"ip_is_private": true, "outbound": "direct"}, {"inbound": ["mixed-in2"], "outbound": "select2"}, {"inbound": ["mixed-in3"], "outbound": "select3"}, {"clash_mode": "Direct", "outbound": "direct"}, {"clash_mode": "Global", "outbound": "select"}, {"type": "logical", "mode": "or", "rules": [{"port": 853}, {"network": "udp", "port": 443}, {"protocol": "stun"}], "outbound": "block"}], "auto_detect_interface": false}, "experimental": {"cache_file": {"enabled": true, "store_rdrc": true}, "clash_api": {"external_controller": "0.0.0.0:19090", "external_ui": "ui", "external_ui_download_url": "https://github.com/MetaCubeX/metacubexd/archive/refs/heads/gh-pages.zip", "external_ui_download_detour": "select", "default_mode": "Rule"}}}