# -*- coding: utf-8 -*-
# by @嗷呜
import colorsys
import random
import re
import sys
from base64 import b64decode, b64encode
from email.utils import unquote
from Crypto.Hash import MD5
sys.path.append("..")
import json
import time
from pyquery import PyQuery as pq
from base.spider import Spider

class Spider(Spider):

    def init(self, extend=""):
        pass

    def getName(self):
        return "嗷呜动漫"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def action(self, action):
        pass

    def destroy(self):
        pass

    host='https://www.aowu.tv'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
        'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="134", "Google Chrome";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'dnt': '1',
        'upgrade-insecure-requests': '1',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'referer': f'{host}/',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'priority': 'u=0, i',
    }

    def homeContent(self, filter):
        data=self.getpq(self.fetch(self.host,headers=self.headers).text)
        result = {}
        classes = []
        ldata=data('.wrap.border-box.public-r .public-list-box')
        cd={"新番":"32","番剧":"20","剧场":"33"}
        for k,r in cd.items():
            classes.append({
                'type_name': k,
                'type_id': r,
            })
        videos=[]
        for i in ldata.items():
            j = i('.public-list-exp')
            k=i('.public-list-button')
            videos.append({
                'vod_id': j.attr('href').split('/')[-1].split('-')[0],
                'vod_name': k('.time-title').text(),
                'vod_pic': j('img').attr('data-src'),
                'vod_year': f"·{j('.public-list-prb').text()}",
                'vod_remarks': k('.public-list-subtitle').text(),
            })
        result['class'] = classes
        result['list']=videos
        return result

    def homeVideoContent(self):
        pass

    def categoryContent(self, tid, pg, filter, extend):
        body = {'type':tid,'class':'','area':'','lang':'','version':'','state':'','letter':'','page':pg}
        data = self.post(f"{self.host}/index.php/api/vod", headers=self.headers, data=self.getbody(body)).json()
        result = {}
        result['list'] = data['list']
        result['page'] = pg
        result['pagecount'] = 9999
        result['limit'] = 90
        result['total'] = 999999
        return result

    def getIdCode(self, id):
        """将数字ID转换为播放页ID格式"""
        try:
            # 尝试通过API获取正确的ID
            api_url = f"{self.host}/index.php/api/vod/detail?id={id}"
            self.log(f"请求API: {api_url}")
            data = self.fetch(api_url, headers=self.headers).json()
            
            if data.get('code') == 1 and data.get('data'):
                # 从API返回的数据中提取正确的ID
                play_url = data['data'].get('vod_play_url', '')
                if play_url:
                    # 播放地址格式: "第01话$https://www.aowu.tv/play/vxQCCS-1-1.html"
                    match = re.search(r'/play/([^-]+)', play_url)
                    if match:
                        return match.group(1)
            
            # 如果API方法失败，尝试直接访问bangumi页面
            bangumi_url = f"{self.host}/bangumi/{id}.html"
            self.log(f"尝试访问番剧页: {bangumi_url}")
            bangumi_data = self.getpq(self.fetch(bangumi_url, headers=self.headers).text)
            
            # 从bangumi页面提取播放链接
            play_link = bangumi_data('a[href*="/play/"]').eq(0).attr('href')
            if play_link:
                match = re.search(r'/play/([^-]+)', play_link)
                if match:
                    return match.group(1)
                    
            return id  # 如果无法转换，返回原始ID
        except Exception as e:
            self.log(f"ID转换失败: {str(e)}")
            return id  # 出错时返回原始ID

    def detailContent(self, ids):
        vid = ids[0]
        try:
            # 检查ID是否为纯数字，如果是则需要转换
            if vid.isdigit():
                code_id = self.getIdCode(vid)
                self.log(f"ID转换: {vid} -> {code_id}")
                vid = code_id
            
            # 访问播放页
            play_url = f"{self.host}/play/{vid}-1-1.html"
            data = self.getpq(self.fetch(play_url, headers=self.headers).text)
            
            # 获取视频信息
            v = data('.player-info-text .this-text')
            title = data('h2').text() or data('h3').text() or vid
            
            vod = {
                'vod_id': vid,
                'vod_name': title,
                'vod_pic': data('img').eq(0).attr('src'),
                'type_name': v.eq(-1)('a').text(),
                'vod_year': v.eq(1)('a').text(),
                'vod_remarks': v.eq(0).text(),
                'vod_actor': v.eq(2)('a').text(),
                'vod_content': data('.player-content').text()
            }
            
            # 获取播放源列表
            ns = data('.swiper-wrapper .vod-playerUrl')
            ps = data('.player-list-box .anthology-list-box ul')
            play, names = [], []
            
            for i in range(len(ns)):
                n = ns.eq(i)('a')
                n('span').remove()
                names.append(re.sub(r"[\ue679\xa0]", "", n.text()))
                play.append('#'.join([f"{v.text()}${v('a').attr('href')}" for v in ps.eq(i)('li').items()]))
            
            vod["vod_play_from"] = "$$$".join(names)
            vod["vod_play_url"] = "$$$".join(play)
            
            result = {"list": [vod]}
            return result
            
        except Exception as e:
            self.log(f"获取详情页失败: {str(e)}")
            return {"list": [{"vod_id": vid, "vod_name": str(vid), "vod_play_from": "", "vod_play_url": ""}]}

    def searchContent(self, key, quick, pg="1"):
        data = self.fetch(f"{self.host}/index.php/ajax/suggest?mid=1&wd={key}&limit=9999&timestamp={int(time.time()*1000)}", headers=self.headers).json()
        videos=[]
        for i in data['list']:
            videos.append({
                'vod_id': i['id'],
                'vod_name': i['name'],
                'vod_pic': i['pic']
            })
        return {'list':videos,'page':pg}

    def playerContent(self, flag, id, vipFlags):
        p, url1 = 1, ''
        yurl = f"{self.host}{id}"
        data = self.getpq(self.fetch(yurl, headers=self.headers).text)
        
        # 获取弹幕数据
        dmhtm = data('.ds-log-set')
        dmdata = {
            'vod_id': dmhtm.attr('data-id') if dmhtm and dmhtm.attr('data-id') else '',
            'vod_ep': dmhtm.attr('data-nid') if dmhtm and dmhtm.attr('data-nid') else ''
        }
        
        try:
            jstr = data('.player-top.box.radius script').eq(0).text()
            if not jstr:
                # 如果找不到播放器脚本，尝试查找其他可能的视频元素
                video_src = data('video').attr('src') or data('iframe').attr('src')
                if video_src:
                    url = video_src
                    p = 0 if re.search(r'\.m3u8|\.mp4', url) else 1
                else:
                    raise Exception("未找到播放器脚本或视频元素")
            else:
                # 处理JSON字符串，确保它是有效的JSON
                json_str = jstr.split('=', 1)[-1].strip()
                if json_str.endswith(';'):
                    json_str = json_str[:-1]
                
                jsdata = json.loads(json_str)
                url1 = jsdata.get('url', '')
                
                if not url1:
                    raise Exception("未找到播放地址URL")
                
                # 解码URL并获取播放器页面
                player_url = f"{self.host}/player/?url={unquote(self.d64(url1))}"
                player_html = self.fetch(player_url, headers=self.headers).text
                player_data = self.p_qjs(self.getjstr(player_html))
                
                # 提取播放地址
                if player_data.get('qualities') and len(player_data['qualities']) > 0:
                    url = player_data['qualities']
                else:
                    url = player_data.get('url', '')
                
                p = 0  # 不需要解析
                
                if not url:
                    raise Exception("未找到有效的播放地址")
        except Exception as e:
            self.log(f"提取播放地址失败: {str(e)}")
            url = yurl
            # 如果直接是媒体URL则直接使用
            if url1 and re.search(r'\.m3u8|\.mp4', url1):
                url = url1
                p = 0
        
        # 构建弹幕URL
        dmurl = f"{self.getProxyUrl()}&data={self.e64(json.dumps(dmdata))}&type=dm.xml" if dmdata.get('vod_id') else ""
        
        return {
            "parse": p, 
            "url": url, 
            "header": {'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'},
            'danmaku': dmurl
        }

    def localProxy(self, param):
        try:
            data = json.loads(self.d64(param['data']))
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'origin': self.host,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            params = {'vod_id': data['vod_id'], 'vod_ep': data['vod_ep']}
            res = self.post(f"https://app.wuyaoy.cn/danmu/api.php/getDanmu", headers=headers, data=params).json()
            danmustr = f'<?xml version="1.0" encoding="UTF-8"?>\n<i>\n\t<chatserver>chat.aowudm.com</chatserver>\n\t<chatid>88888888</chatid>\n\t<mission>0</mission>\n\t<maxlimit>99999</maxlimit>\n\t<state>0</state>\n\t<real_name>0</real_name>\n\t<source>k-v</source>\n'
            my_list = ['1', '4', '5', '6']
            for i in sorted(res['data'], key=lambda x: x['time']):
                dms = [str(i.get('time',1)), random.choice(my_list), '25', self.get_color(), '0']
                dmtxt = re.sub(r'[<>&\u0000\b]', '', self.cleanText(i.get('text', '')))
                tempdata = f'\t<d p="{",".join(dms)}">{dmtxt}</d>\n'
                danmustr += tempdata
            danmustr += '</i>'
            return [200,'text/xml',danmustr]
        except Exception as e:
            print(f"获取弹幕失败：{str(e)}")
            return ""

    def getbody(self, params):
        t=int(time.time())
        h = MD5.new()
        h.update(f"DS{t}DCC147D11943AF75".encode('utf-8'))
        key=h.hexdigest()
        params.update({'time':t,'key':key})
        return params

    def getpq(self, data):
        data=self.cleanText(data)
        try:
            return pq(data)
        except Exception as e:
            print(f"{str(e)}")
            return pq(data.encode('utf-8'))

    def get_color(self):
        h = random.random()
        s = random.uniform(0.7, 1.0)
        v = random.uniform(0.8, 1.0)
        r, g, b = colorsys.hsv_to_rgb(h, s, v)
        r = int(r * 255)
        g = int(g * 255)
        b = int(b * 255)
        decimal_color = (r << 16) + (g << 8) + b
        return str(decimal_color)

    def getjstr(self, data):
        try:
            # 尝试匹配Artplayer配置
            pattern = r'new\s+Artplayer\s*\((\{[\s\S]*?\})\);'
            match = re.search(pattern, data)
            
            if not match:
                # 尝试匹配其他可能的播放器配置
                pattern2 = r'player\s*=\s*new\s+\w+\s*\((\{[\s\S]*?\})\);'
                match = re.search(pattern2, data)
                
            if not match:
                # 尝试匹配简单的JSON配置
                pattern3 = r'var\s+config\s*=\s*(\{[\s\S]*?\});'
                match = re.search(pattern3, data)
            
            config_str = match.group(1) if match else '{}'
            
            # 清理配置中不需要的复杂对象
            replacements = [
                (r'contextmenu\s*:\s*\[[\s\S]*?\{[\s\S]*?\}[\s\S]*?\],', 'contextmenu: [],'),
                (r'customType\s*:\s*\{[\s\S]*?\},', 'customType: {},'),
                (r'plugins\s*:\s*\[\s*artplayerPluginDanmuku\(\{[\s\S]*?lockTime:\s*\d+,?\s*\}\)\,?\s*\]', 'plugins: []'),
                (r'controls\s*:\s*\[[\s\S]*?\],', 'controls: [],'),
                (r'settings\s*:\s*\[[\s\S]*?\],', 'settings: [],'),
                (r'layers\s*:\s*\[[\s\S]*?\],', 'layers: [],'),
                (r'events\s*:\s*\{[\s\S]*?\},', 'events: {},')
            ]
            
            for pattern, replacement in replacements:
                config_str = re.sub(pattern, replacement, config_str)
            
            # 移除可能导致解析错误的JS函数
            config_str = re.sub(r'function\s*\([^)]*\)\s*\{[\s\S]*?\}', '""', config_str)
            
            return config_str
            
        except Exception as e:
            self.log(f"提取播放器配置失败: {str(e)}")
            return '{}'

    def p_qjs(self, config_str):
        try:
            from com.whl.quickjs.wrapper import QuickJSContext
            ctx = QuickJSContext.create()
            js_code = f"""
            function extractVideoInfo() {{
                try {{
                    const config = {config_str};
                    const result = {{
                        url: "",
                        qualities: []
                    }};
                    
                    // 提取主播放地址
                    if (config.url) {{
                        result.url = config.url;
                    }}
                    
                    // 提取多清晰度播放地址
                    if (config.quality && Array.isArray(config.quality)) {{
                        config.quality.forEach(function(q) {{
                            if (q && q.url) {{
                                result.qualities.push(q.html || "嗷呜");
                                result.qualities.push(q.url);
                            }}
                        }});
                    }}
                    
                    // 尝试其他可能的视频源配置
                    if (result.url === "" && config.video && config.video.url) {{
                        result.url = config.video.url;
                    }}
                    
                    if (result.url === "" && config.src) {{
                        result.url = config.src;
                    }}
                    
                    // 尝试从sources数组中获取
                    if (result.url === "" && config.sources && Array.isArray(config.sources) && config.sources.length > 0) {{
                        for (let i = 0; i < config.sources.length; i++) {{
                            if (config.sources[i] && config.sources[i].src) {{
                                result.url = config.sources[i].src;
                                break;
                            }}
                        }}
                    }}

                    return JSON.stringify(result);
                }} catch (e) {{
                    return JSON.stringify({{
                        error: "解析错误: " + e.message,
                        url: "",
                        qualities: []
                    }});
                }}
            }}
            extractVideoInfo();
            """
            result_json = ctx.evaluate(js_code)
            ctx.destroy()
            result = json.loads(result_json)
            
            # 检查是否有错误
            if 'error' in result:
                self.log(f"JS解析错误: {result['error']}")
            
            return result

        except Exception as e:
            self.log(f"QuickJS执行失败: {str(e)}")
            return {
                "error": str(e),
                "url": "",
                "qualities": []
            }

    def e64(self, text):
        try:
            text_bytes = text.encode('utf-8')
            encoded_bytes = b64encode(text_bytes)
            return encoded_bytes.decode('utf-8')
        except Exception as e:
            return ""

    def d64(self,encoded_text):
        try:
            encoded_bytes = encoded_text.encode('utf-8')
            decoded_bytes = b64decode(encoded_bytes)
            return decoded_bytes.decode('utf-8')
        except Exception as e:
            return ""


