{
    "规则名": "七新影视",
    "规则作者": "",
    "请求头参数": "手机",
    "网页编码格式": "UTF-8",
    "图片是否需要代理": "0",
    "是否开启获取首页数据": "1",
    "首页推荐链接": "http://www.7xdy.com",
    "首页列表数组规则": "body&&.stui-vodlist:has(h4)",
    "首页片单列表数组规则": "li",
    "首页片单是否Jsoup写法": "1",
    "分类起始页码": "1",
    "分类链接": "http://www.7xdy.com/{cateId}/index{catePg}.html[firstPage=http://www.7xdy.com/{cateId}/index.html]",
    "分类名称": "电影&电视剧&综艺&动漫",
    "分类名称替换词": "dianyingpian&dianshiju&zongyi&dongman",
    //"筛选数据": {},
    "筛选数据": "ext",
    //{cateId}
    "筛选子分类名称": "动作片&爱情片&科幻片&恐怖片&战争片&喜剧片&一直播&剧情片||国产剧&港台剧&欧美剧&日韩剧",
    "筛选子分类替换词": "dongzuopian&aiqingpian&kehuanpian&kongbupian&zhanzhengpian&xijupian&jilupian&juqingpian||guocanju&gangtaiju&oumeiju&rihanju",
    //{class}
    "筛选类型名称": "",
    "筛选类型替换词": "*",
    //{area}
    "筛选地区名称": "",
    "筛选地区替换词": "*",
    //{year}
    "筛选年份名称": "",
    "筛选年份替换词": "*",
    //{lang}
    "筛选语言名称": "",
    "筛选语言替换词": "*",
    //{by}
    "筛选排序名称": "时间&人气&评分",
    "筛选排序替换词": "time&hits&score",
    "分类截取模式": "1",
    "分类列表数组规则": ".stui-vodlist&&li",
    "分类片单是否Jsoup写法": "1",
    "分类片单标题": "h4&&a&&Text",
    "分类片单链接": "h4&&a&&href",
    "分类片单图片": ".lazyload&&data-original",
    "分类片单副标题": ".pic-text&&Text",
    "分类片单链接加前缀": "http://www.7xdy.com",
    "分类片单链接加后缀": "",
    "搜索请求头参数": "User-Agent$手机",
    "搜索链接": "http://www.7xdy.com/search.php?page=1&searchword={wd}&searchtype=",
    "POST请求数据": "searchword={wd}",
    "搜索截取模式": "1",
    "搜索列表数组规则": ".stui-vodlist__media&&li",
    "搜索片单是否Jsoup写法": "1",
    "搜索片单图片": ".lazyload&&data-original",
    "搜索片单标题": "h3&&a&&Text",
    "搜索片单链接": "h3&&a&&href",
    "搜索片单副标题": ".pic-text&&Text",
    "搜索片单链接加前缀": "http://www.7xdy.com",
    "搜索片单链接加后缀": "",
    "链接是否直接播放": "0",
    "直接播放链接加前缀": "",
    "直接播放链接加后缀": "",
    "直接播放直链视频请求头": "",
    "详情是否Jsoup写法": "0",
    "类型详情": "类型：</span>&&</a>",
    "年代详情": "年份：</span>&&</a>",
    "地区详情": "地区：</span>&&</a>",
    "演员详情": "主演：</span>&&</p>",
    "简介详情": "简介：</span>&&</p>",
    "线路列表数组规则": "body&&#playlist",
    "线路标题": "h3&&Text",
    "播放列表数组规则": "body&&.stui-content__playlist",
    "选集列表数组规则": "li",
    "选集标题链接是否Jsoup写法": "1",
    "选集标题": "a&&Text",
    "选集链接": "a&&href",
    "是否反转选集序列": "1",
    "选集链接加前缀": "http://www.7xdy.com",
    "选集链接加后缀": "",
    "分析MacPlayer": "0",
    "是否开启手动嗅探": "1",
    "手动嗅探视频链接关键词": ".mp4#.m3u8#.flv#video/tos#pt=m3u8#obj/tos-#video/tos#mime_type=video#x-oss-signature=",
    "手动嗅探视频链接过滤词": ".html#=http"
}