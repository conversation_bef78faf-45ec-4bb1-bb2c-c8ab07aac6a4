{"spider": "./pg.jar", "sites": [{"key": "豆瓣", "name": "豆瓣", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 0, "changeable": 1, "indexs": 1, "ext": "./lib/tokenm.json$$$./lib/douban.json"}, {"key": "TGDouban", "name": "TG豆瓣", "type": 3, "api": "csp_TGDouban", "timeout": 120, "ext": {"token": "./lib/tokenm.json", "json": "./lib/tgsearch.json", "keywords": "名称,片名,推荐", "tgsearch_url": "hhttps://tgs6.120572.xyz", "tgsearch_media_url": "hhttps://tgs6.120572.xyz", "channellist": "tgsearchers,leoz<PERSON><PERSON>,ucpanpan,pan123pan,zyfb123,zyzhpd123,xx123pan,tianyi_pd2,tian<PERSON><PERSON><PERSON>,tyypzhpd,cloudtianyi,kuakeclound,ydypzyfx,clouddriveresources,NewQuark,New_Sharing,alypzyhzq|1000,Mbox115|1000,shares_115|1000,wanwan<PERSON>b<PERSON>t|1000", "proxy": "noproxy", "douban": "./lib/douban.json", "danmu": true}, "style": {"type": "rect", "ratio": 0.7}}, {"key": "AList", "name": "网盘|Alist[jar]", "type": 3, "api": "csp_AList", "searchable": 1, "filterable": 1, "changeable": 1, "ext": "./alistjar.json"}, {"key": "虾仁 66", "name": "Emby 4K", "type": 3, "api": "./py/emby_proxy.py", "searchable": 1, "quickSearch": 1, "filterable": 1, "timeout": 60, "ext": {"server": "https://shsiis.cloudfisher.cc:443", "username": "j<PERSON>hi", "password": "a4AXEtxVIL", "thread": 0, "proxy": ""}, "changeable": 1}, {"key": "网盘配置", "name": "网盘及彈幕配置", "type": 3, "api": "csp_Config", "searchable": 0, "changeable": 0, "ext": "./lib/tokenm.json", "style": {"type": "rect", "ratio": 1.5}}, {"key": "xlcsp_XYQHiker_4KVM", "name": "4KVM影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/4KVM.json"}, {"key": "py_光速", "name": "光速｜py", "type": 3, "api": "./py/py_光速.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "cz", "name": "4", "type": 4, "api": "http://192.168.5.187:8080/vod?sites=kvm4", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_4kvm", "name": "4kvm｜py", "type": 3, "api": "./py/4kvm.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_kim<PERSON>dnk", "name": "bttwoo｜py", "type": 3, "api": "./py/bttwoo.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速CliCli动漫APP", "name": "CliCli动漫APP｜py", "type": 3, "api": "./py/CliCli动漫APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速CliClihdmoliAPP", "name": "hbottv｜py", "type": 3, "api": "./py/HBOTTV.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速MiFunAPP", "name": "MiFunAPP｜py", "type": 3, "api": "./py/MiFunAPP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速统一影视MiFunAPP", "name": "统一影视｜py", "type": 3, "api": "./py/统一影视.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速统一4K影视MiFunAPP", "name": "4KVM影视｜py", "type": 3, "api": "./py/kvm.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速MiFunAPP播剧网影视.py", "name": "播剧网｜py", "type": 3, "api": "./py/播剧网.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速MiFunAPP4KAV", "name": "4KA｜py", "type": 3, "api": "./py/4KAV.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光APP4KAV柯南影视", "name": "柯南影视｜py", "type": 3, "api": "./py/柯南影视.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光APP厂长资源柯南影视", "name": "厂长资源｜py", "type": 3, "api": "./py/厂长资源.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光APP4KAV海马影视", "name": "海马影视APP｜py", "type": 3, "api": "./py/海马影视APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_零度影视APP", "name": "零度影视APP｜py", "type": 3, "api": "./py/零度影视APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速云端APP", "name": "云端APP｜py", "type": 3, "api": "./py/云端APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速云端APP奇迹APP", "name": "奇迹APP｜py", "type": 3, "api": "./py/奇迹APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速云端APP偷乐短剧", "name": "偷乐短剧｜py", "type": 3, "api": "./py/偷乐短剧.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速云端APP剧粑粑", "name": "剧粑粑｜py", "type": 3, "api": "./py/剧粑粑.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速云速影视APP", "name": "云速影视APP｜py", "type": 3, "api": "./py/云速影视APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速云速影视热播APPAPP", "name": "热播APP｜py", "type": 3, "api": "./py/热播APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速恋鱼影视APP", "name": "恋鱼影视APP｜py", "type": 3, "api": "./py/恋鱼影视APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速恋鱼影视旺旺APP", "name": "旺旺｜py", "type": 3, "api": "./py/旺旺.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速魔方影视APP", "name": "魔方影视APP｜py", "type": 3, "api": "./py/魔方影视APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "LREEOK", "name": "LREEOK｜py", "type": 3, "api": "./py/LREEOK.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "LREEOK骚火电影", "name": "骚火电影｜py", "type": 3, "api": "./py/骚火电影.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "LREEOK小红影视", "name": "小红影视｜py", "type": 3, "api": "./py/小红影视.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "LREEOK嗷呜动漫", "name": "嗷呜动漫｜py", "type": 3, "api": "./py/嗷呜动漫.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速三号动漫APP", "name": "三号动漫APP｜py", "type": 3, "api": "./py/三号动漫APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速悠悠APP", "name": "悠悠APP｜py", "type": 3, "api": "./py/悠悠APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速悠悠国外剧APPAPP", "name": "国外剧APP｜py", "type": 3, "api": "./py/国外剧APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "hipy_js_美柏", "name": "美柏┃视频", "type": 3, "api": "./py/pymp.py", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "py_光速爱瓜TVAPP", "name": "爱瓜TVAPP｜py", "type": 3, "api": "./py/爱瓜TVAPP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_金牌", "name": "金牌", "type": 3, "api": "./py/金牌.py", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": {"site": "https://www.jiabaide.cn,https://cqzuoer.com"}}, {"key": "py_光速app", "name": "光速App｜py", "type": 3, "api": "./py/光速APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_若惜追剧APP", "name": "若惜追剧APP｜py", "type": 3, "api": "./py/若惜追剧APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_光速app哇哇APP", "name": "哇哇APP｜py", "type": 3, "api": "./py/哇哇APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_红果网页", "name": "红果网页｜py", "type": 3, "api": "./py/红果网页.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_边缘", "name": "边缘｜py", "type": 3, "api": "./py/边缘影视APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_cy", "name": "cy｜py", "type": 3, "api": "./py/cy.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_hitv", "name": "hitv｜py", "type": 3, "api": "./py/py_hitv.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_小苹果APP", "name": "小苹果APP｜py", "type": 3, "api": "./py/小苹果APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_视觉APP", "name": "视觉APP｜py", "type": 3, "api": "./py/视觉APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_胖虎APP", "name": "胖虎APP｜py", "type": 3, "api": "./py/胖虎APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_美帕APP", "name": "美帕APP｜py", "type": 3, "api": "./py/美帕APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_绝对影视", "name": "绝对影视｜py", "type": 3, "api": "./py/绝对影视.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "py_火车太顺APP", "name": "火车太顺APP｜py", "type": 3, "api": "./py/火车太顺APP.py", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60}, {"key": "WebDAV", "name": "WebDAV[jar]", "type": 3, "api": "csp_WebDAV", "searchable": 1, "filterable": 1, "changeable": 1, "timeout": 60, "playerType": 2, "ext": "./lib/webdav.json"}, {"key": "xm娱乐", "name": "🎮游戏┃娱乐", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "./lib/apiv227LIVES.py", "style": {"type": "rect", "ratio": 1.333}, "ext": ""}, {"key": "xm88看球", "name": "🏀看球┃直播", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "order_num": 0, "changeable": 0}, {"key": "xm天天", "name": "🦢天天┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppRJ", "ext": "sHR2rlsfjI4L3t4RXQMknxhunFUlA4159TKiKvIPpfcM1xianxebcSLajBbwFymqC+z9WoGzQYbh7FSvh8KdiC0BKF0CalaPaCEMOZm+ClGEeNzXAaR0FnrV04SiB2NK"}, {"key": "xm热热", "name": "🔥热热┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "./lib/apiv299rebo.py"}, {"key": "xmQD4K", "name": "🐷猪猪┃QD4K", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_AppYsV2", "playerType": 2, "ext": "http://**************:1010/api.php/app/"}, {"key": "xmcsp_Wwys", "name": "👨农民┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_Wwys", "ext": "https://www.wwgz.cn"}, {"key": "xm大梦", "name": "🏆悠悠┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMkn9QhUgBx3GxtTiVGKW2EUdoznFA44qgk+2UMgFRBsVPTy5Vuu99xqFetKxAP1cesStoS652zBvk45bfssFJss98/ortS2qhr2lA1mE/gUFVzB1F/NCkbvR04HM6/elu1JXMu1pzEwvZMFtqUQNyun+OtDwbiiVo1zTHngiWb/vgE"}, {"key": "xm彼岸", "name": "🏖比岸┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMkn0ZmfVTKTXBPRcz0emJNw7pive6/WM9lust5Mt+RF4YRlDNEqLNoLHMx2qn+8fd0eeM4V2kMkA0AtjiEOHJ8KkF1wXdkUnKYxfPeZzUR9DPL4ibrDZHcqEiciYyRtFbBPJ6wn4UwyK3sRMYvcp88bKXb1kZFtpPzbXRluHIQ+0V8YcKK00L4/rQRn30RHCf5UA=="}, {"key": "xm仓鼠", "name": "🐀仓鼠┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMkn97DqO5wP33F1y10iaPo6JFu7Gir00j3R0d6YiQaenvx6EGiBJ/LlFQxikHgxre5aUQs2IWp/rVBIYH9Z5N2lw6Wr6XNDhBuprneT4IeVDEibuFC81JN91ioyOFhZ0Eay51Y9BbamMP6wCWeW6UKiIOLLKTzVCa8r7ptIZGmX6oDimujZatCIppyCf81QNmW2g=="}, {"key": "xm再看", "name": "🔭再看┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMknz+i4ffRorIRZjjcJiBr38wb5nwBciO6ms6RNqBIVi1QB7PRspm4f6MrvIOTd++7FMqkk2yG1eGTUUzyqIbYT58chQYyOMy+COm7unddRux9fFr5h0OEYTptHw9PnRiTV+JAXmIsIgUDKAamVsoOxAyhlMsA0WCzqY0OMMVhCnjjZNeioODGEF/HaKVwMC7i7GkxX/1SkEhN54oZYjGADVp/JQD55mycg7C8p5BmUHb0"}, {"key": "xmcsp_Lgyy", "name": "⏳️️流光┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_FreeOk"}, {"key": "xm牛牛", "name": "🐂牛牛┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "./lib/apiv445mioaying.py", "ext": ""}, {"key": "xm光速", "name": "☁️云端┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMknxNA7c6hcC3VanmE12u0Xu3CTtFtxRELPpvYrBPoXSST9rM4/zqxtg1ZlYdAfbBcu8j2ixF3Zl35t0r1NRL4FKDRtJIzUE6I7MVJAA2Q7brwZK+MVlV3Ya12XCFMmiAzIuP0q31Q3cWJWeBlJ/HkevUD3YrCzVx/d7ctov8ddn/OUS1xjTCIpJ+asEfpCWB+DTxkjbl8kfze+MSaichpJzQDMK2bWUphsOPryk49lVX7Gnm5A77V9sipi2ia5fPcYACDcbL9VXi31e2BvqNcvmE="}, {"key": "xm咕咕", "name": "🛹番剧┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMknxpaIJpWVf3QzJplZjkP8PMRCMAhDX6EuvKn3nmHvaAsNUZGiKejIkSYz7ryONX2fTpbNlJxpW1MZZY7Zt3lbwTJw/1s2QIhPpqxOmR/0EWAwYNShA0Asl0C1lkcmCbV6PsNna5kYEh7as/WabRCm9KrPTGJu6URWhPGeAaWchMgMYJlwNXFIeA2neoQGplg4A=="}, {"key": "xm人人", "name": "⛄️人人┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMknw+zXdsvYSFPrylVBk8Otj7BtrOcImUIxu7uCmi/oCEVSH+1Tuw/tXHzW47SGQsj8bRydPRgWesjHAxv9eu8eO9hOGeUeBKsucMHODs09HXY4kiPp3p1vemBghpvJFm9mx/FFTQYaEQ33Bpqwk73VkDa2zWgoB0CNuBseJhbj2hRKqOiT08iymzh4sSfhjJ5tbS5yTDxPIJAjwMijWuyOJB++d859vxajKD2Jg5qw0lv"}, {"key": "xm电影", "name": "🎬电影┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMkn9964m8oyAP5Q1pq67Vyh7V8ESTgG/jVLrcHt3eKCMbyNGbDyZFu9n7Zd6zMxZJXTkhGtT9DNgJA5YOi9roHwbrmlgGw3de3X42kvYAYCcSnIqa9zR24cDQyq4GwTX7H3QnCkd4xZNrHiLxgPc6nD8L96MLxkLnsYlMQbDTPCOQW"}, {"key": "xm享趣", "name": "🔎享趣┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_AppGet", "ext": "sHR2rlsfjI4L3t4RXQMkn0eYZTuILswGwxMlX75frrEL8QJjjQDTq1hGIbLmL7k8+QY3d6Nh+B6yvptgD1Wj1c4gikVOerYCKY8jbrFo1M1dpW4vCAPm3gz2utYgdUV6PiwSu4Dg4Vdz4W9lHK0ThV3A8bE9P58UtGOkVHrc+I6/lskCKXkPbgijKyCSyG8YSe9IDiOOCl7qn6z/Q93cKg=="}, {"key": "xmcsp_AppXY", "name": "🐸上头┃短剧", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_AppXY"}, {"key": "xmcsp_SAOHUO", "name": "🔥火火┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_<PERSON><PERSON><PERSON>"}, {"key": "xmcsp_baibai", "name": "🦵白白┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_SuBaiBai", "ext": "https://www.subaibai.com"}, {"key": "xmcsp_LiteApple", "name": "🍎苹果┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_LiteApple"}, {"key": "xmcsp_Gz360", "name": "🍉瓜子┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_Gz360"}, {"key": "xm步步", "name": "👟步步┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "./lib/apiv643drpy2.min.js", "ext": "./lib/ceshi.js"}, {"key": "xmcsp_Jpys", "name": "🥇金牌┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 1, "api": "csp_Jpys"}, {"key": "xm海马", "name": "🐎海马┃1080P", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "quickSearch": 0, "api": "./lib/apiv679haima.py", "ext": ""}, {"key": "xm听书", "name": "📚听书┃BOOK", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_<PERSON>g<PERSON>ook", "searchable": 1, "ext": "http://www.6yueting.com"}, {"key": "xmAid", "name": "🚑有来┃急救", "type": 3, "jar": "./xm.jar;md5;bcb27d2740df8d21bfc49c507432663a", "api": "csp_FirstAid", "searchable": 0, "style": {"type": "rect", "ratio": 3.8}}, {"key": "xldrpy_js_奇珍异兽", "name": "奇珍异兽[js]", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "ext": "./lib/奇珍异兽.js"}, {"key": "xldrpy_js_优酷", "name": "优酷[js]", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "ext": "./lib/优酷.js"}, {"key": "xldrpy_js_腾云驾雾", "name": "腾云驾雾[js]", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "ext": "./lib/腾云驾雾.js"}, {"key": "xldrpy_js_百忙无果", "name": "百忙无果[js]", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "ext": "./lib/百忙无果.js"}, {"key": "xlcsp_Mp4Mov", "name": "🧲Mp4电影", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_Mp4Mov", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://www.skymp4.com"}, {"key": "xlcsp_New6v", "name": "🧲新6V", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_New6v", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://www.xb6v.com"}, {"key": "xlcsp_DyGod", "name": "🧲电影天堂", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_DyGod", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "xlcsp_QnMp4", "name": "🧲七妹", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_QnMp4", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "xlcsp_SeedHub", "name": "🧲SeedHub", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_SeedHub", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://www.seedhub.cc"}, {"key": "xlcsp_MeijuTT", "name": "🧲美剧天堂", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_Meiju<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://www.meijutt.net"}, {"key": "xlcsp_BLSGod", "name": "🧲80S影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_BLSGod", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "xlcsp_Xunlei8", "name": "🧲迅雷吧", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON>i8", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "xl360_spider", "name": "🐞360影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_SP360", "filterable": 1, "quickSearch": 1, "searchable": 1}, {"key": "xlcsp_Kuaikan", "name": "💡快看影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "xlcsp_LiteApple", "name": "🐞小苹果影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_LiteApple", "filterable": 1, "quickSearch": 1, "searchable": 1}, {"key": "xlcsp_Bdys", "name": "🐞哔嘀影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_Bdys", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://v.xlys.ltd.ua"}, {"key": "xlcsp_XYQHiker_修罗影视", "name": "🧲修罗影视(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/哔嘀影视.json"}, {"key": "xlcsp_Ddys", "name": "🐞低端影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_Ddys", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "xlcsp_Jian<PERSON>ian", "name": "🔨荐片", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "playerType": 1, "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://apijp.rinhome.com"}, {"key": "xlcsp_Ikanbot", "name": "👾Ikanbot", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON>bot", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://v.aikanbot.com"}, {"key": "xlcsp_Bili幼儿", "name": "🐞哔哩幼儿", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/幼儿乐园.json"}, {"key": "xlcsp_Bili少儿", "name": "🐞哔哩少儿", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/少儿教育.json"}, {"key": "xlcsp_Bili小学", "name": "🐞哔哩小学", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/小学课堂.json"}, {"key": "xlcsp_Bili初中", "name": "🐞哔哩初中", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/初中课堂.json"}, {"key": "xlcsp_Bili高中", "name": "🐞哔哩高中", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/高中课堂.json"}, {"key": "xlJS哔哩直播", "name": "哔哩直播[js]", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/哔哩直播.js"}, {"key": "xlcsp_XYQHiker_戏曲多多", "name": "🎻戏曲多多", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/戏曲多多.json"}, {"key": "xlcsp_XYQHiker_酷奇MV", "name": "🎤酷奇MV(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/酷奇MV.json"}, {"key": "xlcsp_XYQHiker_短剧屋", "name": "🎬短剧屋", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/短剧屋.json"}, {"key": "xlcsp_XYQHiker_兔小贝", "name": "🐰兔小贝(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/兔小贝.json"}, {"key": "xlcsp_XYQHiker_兔小贝2", "name": "🐰兔小贝2(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 0, "quickSearch": 1, "filterable": 1, "ext": "./lib/兔小贝2.json"}, {"key": "xlcsp_XYQHiker_播视童趣", "name": "👶🏻播视童趣(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "./lib/播视童趣.json"}, {"key": "xlcsp_XYQHiker_风车动漫", "name": "🌪风车动漫(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/风车动漫.json"}, {"key": "xlcsp_XYQHiker_樱花动漫", "name": "🌸樱花动漫(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/樱花动漫.json"}, {"key": "xlcsp_XYQHiker_去看吧动漫", "name": "🔭去看吧动漫(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/去看吧动漫.json"}, {"key": "xlcsp_XYQHiker_嗷呜动漫", "name": "🙀嗷呜动漫(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/嗷呜动漫.json"}, {"key": "xlcsp_XYQHiker_动漫巴士", "name": "🚌动漫巴士(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/动漫巴士.json"}, {"key": "xlcsp_XYQHiker_农民影视", "name": "🧑🏻‍农民影视", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/农民影视.json"}, {"key": "xlcsp_XYQHiker_剧圈圈", "name": "🌟剧圈圈(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/剧圈圈.json", "click": "document.getElementById('playleft').children[0].contentWindow.document.getElementById('start').click()"}, {"key": "xlcsp_XYQHiker_骚火电影VIP", "name": "🔥骚火电影VIP(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/骚火电影VIP.json"}, {"key": "xlcsp_XYQHiker_电影盒子", "name": "📦电影盒子", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/电影盒子.json"}, {"key": "xlcsp_XYQHiker_星辰影院", "name": "⭐️星辰影院(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/星辰影院.json"}, {"key": "xlcsp_XYQHiker_可可影视", "name": "☕️可可影视(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/可可影视.json"}, {"key": "xlcsp_XYQHiker_爱看影视", "name": "👀爱看影视(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/爱看影视.json"}, {"key": "xlcsp_XYQHiker_看一看影视", "name": "🔍看一看影视(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/看一看影视.json"}, {"key": "xlcsp_XYQHiker_瓜子影院", "name": "瓜子影院(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/瓜子影视.json"}, {"key": "xlcsp_XYQHiker_八号影视", "name": "8️⃣八号影视(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/八号影视.json"}, {"key": "xlcsp_XYQHiker_来看点播", "name": "⛅️来看点播(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./lib/来看点播.json"}, {"key": "xldrpy_js_金牌", "name": "金牌影视[js]", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "ext": "./lib/金牌影视.js"}, {"key": "xl柚子资源", "name": "🍊柚子资源", "type": 0, "api": "https://api.yzzy-api.com/inc/api.php", "searchable": 1, "quickSearch": 1, "categories": ["动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片", "国产剧", "台湾剧", "韩国剧", "欧美剧", "香港剧", "泰国剧", "日本剧", "福利", "记录片", "动画片", "海外剧", "倫理片", "大陆综艺", "港台综艺", "日韩综艺", "欧美综艺", "国产动漫", "日韩动漫", "欧美动漫", "港台动漫", "海外动漫", "搞笑", "音乐", "影视", "汽车", "短剧大全", "预告片", "预告片", "体育"]}, {"key": "xlDRJS_虎牙", "name": "虎牙直播(JS)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "./lib/apiv127drpy2.min.js", "ext": "./lib/虎牙直播.js", "searchable": 0, "quickSearch": 0, "filterable": 1}, {"key": "xlcsp_XYQHiker_虎牙直播", "name": "🐯虎牙直播(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 0, "filterable": 1, "ext": "./lib/虎牙直播.json"}, {"key": "xlcsp_XYQHiker_斗鱼直播", "name": "🐠斗鱼直播(XYQH)", "type": 3, "jar": "./xl.jar;md5;6f86d70c110eb76ad81084beeffaa198", "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 0, "filterable": 1, "ext": "./lib/斗鱼直播.json"}, {"key": "qj天天", "name": "天天┃高峰期用不了就点我换源或者搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_TTian", "playerType": 1, "ext": "7lj763gg09397919456493i0h44j8681highi4"}, {"key": "qjcsp_xlys", "name": "修罗┃无搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Bdys01", "searchable": 1, "playerType": 1, "quickSearch": 1, "filterable": 0, "ext": "7lj763gg402i7942476492jlg94li29kk6gi8448ij"}, {"key": "qj热播", "name": "热播┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_TTian", "playerType": 1, "ext": "7lj763gg0939791h1l3888jig44gi291li"}, {"key": "qj追剧", "name": "追剧┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_TTian", "playerType": 1, "ext": "7lj763gg0939791h1l2681i6g94li291li"}, {"key": "qj剧评", "name": "剧评┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Jpys", "playerType": 1, "ext": "7lj763gg402i7942463j9j9jh84l8798l8gli652828g332i"}, {"key": "qj巧技二", "name": "巧技┃仅搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_qiao2", "playerType": 2, "ext": "7lj763gg402i79425739i7jghj118797l4hj840gi18633331l4708g2h7145403549g44l8ii56i187681hkjj3hhgh1ih3l32j250lk1k786lj20j468hk3hli4l46gig4i3g7g2722328j0136h01i7g5183k22k7gg3i72hk81gl8k9839kl7i0707"}, {"key": "qj巧技三", "name": "巧技三┃QD4K", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_AppYsV2", "playerType": 2, "ext": "7lj763gg0939795i0678i48hk312jji7g4k69h089ij36j71031154l2k91k151k138612h68842ji"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "橘子┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg093979191l288ij5g25989jil1g6850i829j24354k134hg2gi41025i4hi708k1i100i3j56k01klj3g7g94khkg230250hh2kh99gi77j021g03llj"}, {"key": "qjyi<PERSON><PERSON>", "name": "驿站┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON><PERSON>", "playerType": 2, "ext": "7lj763gg402i7942403h83i2h945858hljhji148i18k2837535112l2qiaojik9075l17028i49g192419i8g3245h3j1l9gi02h6k7732650h3h09jkg759j65hj39l50347k3gj97l4g12l7h6418h6k9j04l26i1glgj3631973hh280lkihjh "}, {"key": "qj主角", "name": "主角┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON><PERSON>", "playerType": 2, "ext": "7lj763gg402i794247258k9jh6598585l3g6ij13il8g20g9qiaoji8j9i9k1g3k90h7i507i213k5j602"}, {"key": "qj永夜", "name": "永夜┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON><PERSON>", "playerType": 2, "ext": "7lj763gg0939790i413gi4jjh545959lhighi4414qiaoji964ihg48l909l2l019kgii607i213k5j602"}, {"key": "qj专业", "name": "专业┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 1, "ext": "7lj763gg402i7942503g9gj0hj4li291k1lii113j8862i3j7g5412g5gi4k0k5i4ki513l9j61ijjig2735g88gg0g941h2g2352710ggli85h268996ig539"}, {"key": "qj<PERSON>", "name": "星河┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg0939791954248jihg5529587lig9i654j59i326l5j5208hklg19161j139753h090539gig3g11h49gk3l21hl4hk637h53ghg6ikl2219j28lj76h8430kkkklj6hkl022272g8h5g69068hli583061k3l9373hk09hg9j79l31l43h06187l37j675lhkg57k511jk"}, {"key": "qj时常", "name": "时常┃无搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg0939795i007hi482k80ii2i3ghl084099ij36j70031107l2kg105816139i56g38h03i1j06k4gkk9ll7kl1lk4k8687k55l0g19ggk72j06gg073li1j11"}, {"key": "qj晴空", "name": "晴空┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg402i7942463j9j9jg8558j9jlgh1j913il86313j776l14ljjh3l5k383999648j9064g6h22738938il8jk30k2i6427h67jkhglji910996ig539"}, {"key": "qj鲨鱼", "name": "鲨鱼┃无搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg402i79425227999jg7508k90k3k6j244j6952l2557574gg6kh00480l42i05hg49i578l973l18ghj2g3kk0ll4l1392258l7h6ikk077j56l"}, {"key": "qj雨滴", "name": "雨滴┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg402i7942482k93i0h4458293llg1i959i3843j285i4l1glkli590j56559144kji902i5j36352hkjglhgk12l4g2392i46hik0i3gg698964lk37gl5512g9lj90gjg4677h69j046255h93lg416576hkl76l2kk8i0gj888k67h5700h0l6438ig74k8k148kk45j8242l375j9g4789j64023i313847k10g62i40i3ik00634127154957"}, {"key": "qj优质", "name": "优质┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg402i79425j2893i0h14i8596k6h1i75gj4i76g791i4602g9g8410i5642j951h5814192943219g99glhh319l7k02k6046l0gkigki2l842jk363gl5453l1l2jhl3g02l3k798l4h3j4582lh4g7l61hgll7776h096h0j4jh7k"}, {"key": "qj木叶", "name": "木叶┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Xdai", "playerType": 2, "ext": "7lj763gg0939795i0079i487k512jki6g1k6980k9li631344h4455k7k103473i0kh8049hgg4484h0091ggjh2l8ki33lhi2755g07i880jjkh12h73gk35kg35j6hk6g296l5"}, {"key": "qj蝴蝶", "name": "蝴蝶┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON><PERSON>", "playerType": 2, "ext": "7lj763gg402i794255648kj0hj539g9hl7k6jk52ji"}, {"key": "qj柠檬", "name": "柠檬┃App", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Nmvod", "playerType": 2, "ext": "7lj763gg402i7942503g839jg74h8h88highi40799jh6i6k454419l0l5184k0g548458hh994hjj94330jk296k4l045"}, {"key": "qjAI2", "name": "AI2┃仅搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 0, "playerType": 2, "ext": "7lj763gg0939795i067li482k704i2igg2k6980j95i631231j400hlgh707531k54864lh88k5h9781740hh287h9k035k8kg697852k5lj9kh276i575jk6kgk444kkkl8j1gggj370h768h473015hll64h265j82hg5667h79k93j4jk16l6215l327l71i922ilh6499j4k9h66297k5gjh4ii39j5107h837lh5104k477449hj1104h1i384l164k779h4595jlkh577l8i6570l23h4hg71hj92243h9076glj4k1k3il2404gl2i4i445ii0ji55578"}, {"key": "qjAI3", "name": "AI3┃仅搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 0, "playerType": 2, "ext": "7lj763gg0939795i067li482k704i2igg2k6980j95i631231j5203kgkg585g1k12j84jhl881j8396340hh487l3h30kk8l42l6851klhgjlk836ig69hj36k31i5ikhlki3gjli7k"}, {"key": "qj<PERSON><PERSON><PERSON><PERSON>", "name": "电影港[边下边播]", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON><PERSON>gang"}, {"key": "qjLib", "name": "Lib┃直连", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Lib", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "7lj763gg402i7942463j9j9jhi558k84lhg7845kili62l35535512l0h61j48410h9g5igk9j40908h351hh484h8l60h"}, {"key": "qjNoSearch_csp_DiDuan", "name": "低端┃无搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Ddrk", "searchable": 1, "playerType": 2, "quickSearch": 1, "filterable": 0}, {"key": "qj快看", "name": "快看┃直连", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_<PERSON>", "searchable": 1, "playerType": 1, "quickSearch": 1, "filterable": 1}, {"key": "qj360_spider", "name": "360┃解析", "api": "csp_SP360", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "filterable": 1, "quickSearch": 1, "searchable": 1, "playerType": 2, "ext": ""}, {"key": "qjZXZJ", "name": "在线┃直连", "api": "csp_Zxzj", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "filterable": 1, "quickSearch": 1, "searchable": 1, "playerType": 2, "ext": "7lj763gg402i7942463j9j9jgg449698khhh845ki38473"}, {"key": "qjcsp_CZSPP", "name": "厂长┃直连", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Czsapp", "searchable": 1, "playerType": 1, "quickSearch": 1, "filterable": 0, "ext": "7lj763gg402i7942463j9j9jh346968hg5ll845ki38473"}, {"key": "qjcsp_sbb", "name": "布布┃直连", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Czsapp", "searchable": 1, "playerType": 1, "quickSearch": 1, "filterable": 0, "ext": "7lj763gg402i7942463j9j9jg3498k93lhggih54828g332i1j"}, {"key": "qjcsp_Auete", "name": "Auete┃无搜索", "type": 3, "jar": "./qj.jar;md5;73b06cb7fe8a1208b1afaa332a6a37c3", "api": "csp_Auete", "searchable": 1, "playerType": 1, "quickSearch": 1, "filterable": 1}, {"key": "PushShare", "name": "我的资源分享", "type": 3, "api": "csp_PushShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/pushshare.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "AlistShare", "name": "网盘分享合集", "type": 3, "api": "csp_AlistShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 120, "ext": "./lib/tokenm.json$$$null$$$proxy$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "AliShare", "name": "阿里云盘影视分享", "type": 3, "api": "csp_AliShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/alishare.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "AliShareEBook", "name": "阿里云盘书籍分享", "type": 3, "api": "csp_AliShare", "searchable": 0, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/alishare.ebook.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "QuarkShare", "name": "夸克云盘分享", "type": 3, "api": "csp_QuarkShare", "searchable": 1, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/quarkshare.txt", "style": {"type": "list", "ratio": 1.1}}, {"key": "123Share", "name": "123云盘分享", "type": 3, "api": "csp_P123Share", "searchable": 1, "quickSearch": 1, "changeable": 0, "filterable": 0, "timeout": 120, "ext": "./lib/tokenm.json$$$./lib/123share.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "189Share", "name": "189云盘分享", "type": 3, "api": "csp_P189Share", "searchable": 1, "quickSearch": 1, "changeable": 0, "filterable": 0, "timeout": 120, "ext": "./lib/tokenm.json$$$./lib/189share.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "ThunderShare", "name": "迅雷云盘分享", "type": 3, "api": "csp_ThunderShare", "searchable": 1, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/thundershare.txt", "style": {"type": "list", "ratio": 1.1}}, {"key": "UCShare", "name": "UC云盘分享", "type": 3, "api": "csp_UCShare", "searchable": 1, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/ucshare.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "115Share", "name": "115云盘分享", "type": 3, "api": "csp_P115Share", "searchable": 1, "quickSearch": 1, "changeable": 0, "filterable": 0, "timeout": 120, "ext": "./lib/tokenm.json$$$./lib/115share.txt$$$db$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "PikPakShare", "name": "PikPak分享", "type": 3, "api": "csp_PikPakShare", "searchable": 1, "quickSearch": 1, "changeable": 1, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/pikpakclass.json$$$./lib/pikpakclass.json.txt.gz", "style": {"type": "list", "ratio": 1.1}}, {"key": "SambaShare", "name": "Samba分享", "type": 3, "api": "csp_SambaShare", "searchable": 0, "quickSearch": 0, "changeable": 0, "filterable": 0, "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/sambashare.txt"}, {"key": "FirstAid", "name": "🚑 急救知识 ", "type": 3, "api": "csp_FirstAid", "searchable": 0, "quickSearch": 0, "changeable": 0, "style": {"type": "rect", "ratio": 3.8}}, {"key": "bili", "name": "B站 | Bili[jar]", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 0, "changeable": 0, "timeout": 60, "playerType": 2, "ext": {"type": "演唱会#电影#电视剧#帕梅拉#广场舞#纪录片#综艺#鬼畜#音乐#舞蹈#影视#知识#美食#风光#田园#户外#8K#徐云", "cookie": ""}}, {"key": "TGYunPanLocal", "name": "TG频道搜索", "type": 3, "api": "csp_TGYunPanLocal", "timeout": 120, "ext": {"token": "./lib/tokenm.json", "json": "./lib/tgsearch.json", "keywords": "名称,片名,推荐", "tgsearch_url": "hhttps://tgs6.120572.xyz", "tgsearch_media_url": "hhttps://tgs6.120572.xyz", "channellist": "tgsearchers,leoz<PERSON><PERSON>,ucpanpan,pan123pan,zyfb123,zyzhpd123,xx123pan,tianyi_pd2,tian<PERSON><PERSON><PERSON>,tyypzhpd,cloudtianyi,kuakeclound,ydypzyfx,clouddriveresources,NewQuark,New_Sharing,oneonefivewpfx,hao115,guaguale115,Channel_Shares_115,dianyingshare,XiangxiuNB,yunpanpan,kuakeyun,zaihuayun,Quark_Movies,vip115hot,yunpanshare,shareAliyun,ikiviyyp,alyp_1,quanzi<PERSON>she", "proxy": "proxy", "danmu": true}, "style": {"type": "rect", "ratio": 1.77}}, {"key": "TGYunPan", "name": "TG群组搜索", "type": 3, "api": "csp_TGYunPan", "timeout": 120, "ext": {"token": "./lib/tokenm.json", "json": "./lib/tgsearch.json", "keywords": "名称,片名,推荐", "tgsearch_url": "hhttps://tgs6.120572.xyz", "tgsearch_media_url": "hhttps://tgs6.120572.xyz", "channellist": "alypzyhzq|1000,Mbox115|1000,shares_115|1000,NewQuark|1000,New_Sharing|1000,wanwan<PERSON><PERSON><PERSON><PERSON>|1000", "proxy": "noproxy", "danmu": true}, "style": {"type": "rect", "ratio": 1.77}}, {"key": "Guanying", "name": "观影|网盘|磁力", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.gying.net$$$noproxy$$$1$$$./lib/guanying.txt$$$GUANYING"}, {"key": "FourKFM", "name": "4K影库|网盘|磁力", "type": 3, "api": "csp_FourKFM", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$http://4kfm.com/$$$noproxy$$$1$$$./lib/4kfm.txt$$$4KFM"}, {"key": "校长影视", "name": "校长影视|网盘", "type": 3, "api": "csp_<PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://xzyshd.com$$$proxy$$$1"}, {"key": "<PERSON><PERSON><PERSON>", "name": "玩偶哥哥|网盘", "type": 3, "api": "csp_Wogg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$http://wogg.888484.xyz/$$$noproxy$$$1$$$./lib/wogg.json$$$WOGG"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "玩你老哥|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://tv.yydsys.top/$$$proxy$$$1$$$./lib/wogg.json$$$WNLG"}, {"key": "蜡笔网盘", "name": "蜡笔|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.goimg.asia/$$$noproxy$$$1$$$./lib/wogg.json$$$LABI"}, {"key": "<PERSON><PERSON>", "name": "木偶哥哥|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$http://123.666291.xyz/$$$noproxy$$$1$$$./lib/wogg.json$$$MOGG"}, {"key": "DAWO", "name": "大玩偶|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$http://149.88.87.18:6688/$$$noproxy$$$1$$$./lib/wogg.json$$$DAWO"}, {"key": "BAIJIA", "name": "百家|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$http://cj.jiexi.news/$$$noproxy$$$1$$$./lib/wogg.json$$$BAIJIA"}, {"key": "QINGYING", "name": "清影|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://revohd.com/$$$noproxy$$$1$$$./lib/wogg.json$$$QINGYING"}, {"key": "<PERSON><PERSON>", "name": "小米|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.mucpan.cc/$$$noproxy$$$1$$$./lib/wogg.json$$$XIAOMI"}, {"key": "至臻|网盘", "name": "至臻|网盘", "type": 3, "api": "csp_Wobg", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://mihdr.top/$$$noproxy$$$1$$$./lib/wogg.json$$$ZHIZHEN"}, {"key": "Hmxz", "name": "海绵|网盘", "type": 3, "api": "csp_Hmxz", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://haimian.eu.org/$$$noproxy$$$1$$$./lib/hmxz.txt$$$HMXZ", "style": {"type": "list", "ratio": 1.1}}, {"key": "Leijing", "name": "雷鲸|网盘", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://leijing.xyz/$$$noproxy$$$1$$$./lib/leijing.txt$$$LEIJING", "style": {"type": "list", "ratio": 1.1}}, {"key": "<PERSON><PERSON><PERSON>", "name": "瓜子|App", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": ""}, {"key": "<PERSON><PERSON>", "name": "HDmoli|网盘", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.hdmoli.pro/$$$proxy$$$1$$$./lib/moli.json"}, {"key": "Ppxzy", "name": "皮皮虾|网盘", "type": 3, "api": "csp_Ppxzy", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://ppxzy.ink$$$proxy$$$1"}, {"key": "ddys", "name": "低端影视|网盘", "type": 3, "api": "csp_Ddys", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://ddys.pro/$$$proxy$$$1$$$"}, {"key": "美剧迷", "name": "美剧迷|网盘", "type": 3, "api": "csp_<PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 120, "ext": "./lib/tokenm.json$$$https://www.meijumi.net/$$$proxy$$$1"}, {"key": "<PERSON><PERSON><PERSON>", "name": "LIBVIO|网盘", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.libvio.fun/$$$null"}, {"key": "<PERSON><PERSON><PERSON>", "name": "鸟窝|网盘", "type": 3, "api": "csp_Hdh", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 120, "ext": "./lib/tokenm.json$$$https://hdhive.online$$$noproxy$$$1$$$./lib/hdhive.txt"}, {"key": "TianYiSou", "name": "天逸搜|网盘搜索", "type": 3, "api": "csp_TianYiSou", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$null$$$proxy"}, {"key": "<PERSON><PERSON><PERSON>", "name": "云盘资源|网盘", "type": 3, "api": "csp_<PERSON><PERSON>", "timeout": 60, "ext": "./lib/tokenm.json$$$null$$$proxy$$$1"}, {"key": "Ying<PERSON><PERSON>", "name": "影搜|网盘搜索", "type": 3, "api": "csp_Ying<PERSON>o", "timeout": 60, "ext": "./lib/tokenm.json"}, {"key": "混合盘", "name": "混合盘|网盘搜索", "type": 3, "api": "csp_HunHePan", "timeout": 60, "ext": "./lib/tokenm.json", "style": {"type": "list", "ratio": 1.1}}, {"key": "88Pan", "name": "88网盘|网盘搜索", "type": 3, "api": "csp_EightEight", "timeout": 60, "ext": "./lib/tokenm.json$$$https://662688.xyz$$$", "style": {"type": "list", "ratio": 1.1}}, {"key": "PanSearch", "name": "PanSearch|网盘搜索", "type": 3, "api": "csp_PanSearch", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$null$$$proxy"}, {"key": "盘友圈", "name": "盘友圈|网盘搜索", "type": 3, "api": "csp_Panyq", "timeout": 60, "ext": "./lib/tokenm.json$$$site$$$proxy"}, {"key": "易搜", "name": "易搜|网盘搜索", "type": 3, "api": "csp_<PERSON>o", "timeout": 60, "ext": "./lib/tokenm.json$$$./lib/yiso.txt", "style": {"type": "list", "ratio": 1.1}}, {"key": "秒搜", "name": "秒搜|网盘搜索", "type": 3, "api": "csp_Miao<PERSON>ou", "timeout": 60, "ext": "./lib/tokenm.json", "style": {"type": "list", "ratio": 1.1}}, {"key": "Funlet<PERSON>", "name": "趣盘搜|夸克搜索", "type": 3, "api": "csp_<PERSON>letu", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json", "style": {"type": "list", "ratio": 1.1}}, {"key": "QuarkPanso", "name": "夸克盘搜|夸克搜索", "type": 3, "api": "csp_QuarkPanso", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json", "style": {"type": "list", "ratio": 1.1}}, {"key": "Pan<PERSON>", "name": "盘Ta|网盘", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.91panta.cn/$$$null$$$1$$$./lib/panta.txt$$$PANTA", "style": {"type": "list", "ratio": 1.1}}, {"key": "DaPanSo", "name": "大盘搜|网盘搜索", "type": 3, "api": "csp_DaPanSo", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://dapanso.com$$$proxy$$$1"}, {"key": "PikaSo", "name": "皮卡搜|网盘搜索", "type": 3, "api": "csp_PikaSo", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.pikaso.top/$$$null", "style": {"type": "list", "ratio": 1.1}}, {"key": "<PERSON><PERSON><PERSON>", "name": "千帆|网盘搜索", "type": 3, "api": "csp_<PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://pan.qianfan.app$$$null$$$./lib/qianfan.txt$$$1"}, {"key": "YunSo", "name": "小云搜索|网盘搜索", "type": 3, "api": "csp_<PERSON><PERSON>", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.yunso.net$$$null$$$1", "style": {"type": "list", "ratio": 1.1}}, {"key": "YunPanOne", "name": "云盘One|网盘搜索", "type": 3, "api": "csp_YunPanOne", "quickSearch": 1, "changeable": 1, "filterable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$null$$$proxy$$$1"}, {"key": "新6V", "name": "新6V|网盘", "type": 3, "api": "csp_Xb6v", "searchable": 1, "changeable": 1, "timeout": 60, "ext": "./lib/tokenm.json$$$https://www.66ss.org$$$null$$$1"}, {"key": "黑木耳", "name": "黑木耳", "type": 1, "api": "http://127.0.0.1:10079/p/0/null/https://json.heimuer.xyz/api.php/provide/vod/", "searchable": 1, "quickSearch": 1}, {"key": "索尼资源", "name": "索尼┃资源", "type": 1, "api": "https://suoniapi.com/api.php/provide/vod/from/snm3u8/", "categories": ["动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片", "国产剧", "欧美剧", "韩剧", "日剧", "港剧", "台剧", "泰剧", "海外剧", "纪录片", "大陆综艺", "日韩综艺", "港台综艺", "欧美综艺", "国产动漫", "日韩动漫", "欧美动漫", "动画片", "港台动漫", "海外动漫", "演唱会", "体育赛事", "篮球", "足球", "预告片", "斯诺克", "影视解说"], "searchable": 1, "quickSearch": 1}, {"key": "UP云搜", "name": "阿里|UP云搜", "type": 3, "api": "csp_UpYun", "timeout": 30, "ext": "./lib/tokenm.json", "style": {"type": "list", "ratio": 1.1}}, {"key": "BLSGod", "name": "BLSGod|磁", "type": 3, "api": "csp_BLSGod", "changeable": 1, "timeout": 60}, {"key": "SeedHub", "name": "SeedHub|磁", "type": 3, "api": "csp_SeedHub", "changeable": 1, "timeout": 60}, {"key": "快看", "name": "快看", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 0, "filterable": 1}, {"key": "ikun", "name": "ikun", "type": 1, "api": "http://127.0.0.1:10079/p/0/proxy/https://ikunzyapi.com/api.php/provide/vod/?", "playUrl": "json:http://127.0.0.1:10079/parse/?thread=0&proxy=proxy&url=", "playerType": 1, "searchable": 1, "changeable": 1}, {"key": "暴風", "name": "暴風", "type": 1, "api": "http://127.0.0.1:10079/p/0/proxy/https://bfzyapi.com/api.php/provide/vod?", "playUrl": "json:http://127.0.0.1:10079/parse/?thread=0&proxy=proxy&url=", "searchable": 1, "changeable": 1}, {"key": "索尼", "name": "索尼 ", "type": 1, "api": "http://127.0.0.1:10079/p/0/proxy/https://suoniapi.com/api.php/provide/vod?", "playUrl": "json:http://127.0.0.1:10079/parse/?thread=0&proxy=proxy&url=", "searchable": 1, "changeable": 1}, {"key": "量子", "name": "量子", "type": 1, "api": "http://127.0.0.1:10079/p/0/proxy/http://cj.lziapi.com/api.php/provide/vod/?", "playUrl": "json:http://127.0.0.1:10079/parse/?thread=0&proxy=proxy&url=", "playerType": 1, "searchable": 1, "changeable": 1}, {"key": "非凡", "name": "非凡", "type": 1, "api": "http://127.0.0.1:10079/p/0/proxy/http://cj.ffzyapi.com/api.php/provide/vod/?", "playUrl": "json:http://127.0.0.1:10079/parse/?thread=0&proxy=proxy&url=", "playerType": 1, "searchable": 1, "changeable": 1, "categories": ["国产动漫", "日韩动漫", "国产剧", "韩国剧", "日本剧", "电影片", "连续剧", "综艺片", "动漫片", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片", "香港剧", "欧美剧", "记录片", "台湾剧", "海外剧", "泰国剧", "大陆综艺", "港台综艺", "日韩综艺", "欧美综艺", "欧美动漫", "港台动漫", "海外动漫"]}, {"key": "巴士", "name": "巴士", "type": 3, "api": "csp_Dm84", "searchable": 1, "changeable": 0}, {"key": "異界", "name": "異界", "type": 3, "api": "csp_Ysj", "searchable": 1, "changeable": 0}, {"key": "drpy_js_<PERSON><PERSON>", "name": "网盘 | Alist[js]", "type": 3, "api": "./lib/alist.min.js", "searchable": 1, "filterable": 1, "changeable": 1, "ext": "./js/alist.json"}, {"key": "MV_vod", "name": "电视┃MTV", "type": 1, "api": "https://mv.wogg.link/mv/vod", "searchable": 1, "quickSearch": 0, "changeable": 0}, {"key": "酷狗", "name": "酷狗", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "changeable": 0}, {"key": "hipy_js_KTV歌厅[听]1", "name": "🎤KTV歌厅在线", "type": 3, "api": "http://kxrj.site:55/lib/drpy2.min.js", "searchable": 1, "quickSearch": 1, "filterable": 1, "order_num": 0, "ext": "http://kxrj.site:55/lib/js/KTV歌厅[听].js"}, {"key": "Yin<PERSON><PERSON><PERSON>", "name": "音悦台", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "changeable": 0}, {"key": "push_agent", "name": "推送", "type": 3, "api": "csp_<PERSON>ush", "changeable": 0, "timeout": 30, "ext": "./lib/tokenm.json"}, {"key": "應用商店", "name": "應用商店", "type": 3, "api": "csp_Market", "searchable": 0, "changeable": 0, "ext": "https://fm.t4tv.hz.cz/json/market.json"}, {"key": "直播转点播", "name": "🐼️┃电视┃直播", "type": 3, "searchable": 0, "api": "./lib/live2vod.js", "ext": "./lib/feimaolive.json"}], "lives": [{"name": "golive(4)", "url": "http://**************:8000/hls/txt", "type": 0, "epg": "https://epg.v1.mk/json?ch={name}&date={date}", "logo": "https://epg.v1.mk/logo/{name}.png", "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "golive(6)", "url": "https://gh-proxy.com/raw.githubusercontent.com/vbskycn/iptv/refs/heads/master/tv/iptv6.txt", "type": 0, "epg": "https://epg.v1.mk/json?ch={name}&date={date}", "logo": "https://epg.v1.mk/logo/{name}.png", "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "最强国内直播(合)", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/all.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "最强国内直播", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/tv.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "最强国内直播(EPG-1天回看)", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/tv.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date=DATE1SUB", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "BJYD", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/bjyd.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "BJYD(EPG-1天回看)", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/bjyd.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date=DATE1SUB", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "BJYD(EPG-2天回看)", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/bjyd.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date=DATE2SUB", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "BJYD(EPG-3天回看)", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/bjyd.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date=DATE3SUB", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png", "catchup": {"type": "append", "source": "?livemode=4&starttime=${(b)yyyyMMdd'T'HHmm}00.00Z&endtime=${(e)yyyyMMdd'T'HHmm}00.00Z"}}, {"name": "BIPTV(IPV6,可回看)", "type": 0, "url": "./lib/biptv.txt", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "PandaGo投屏直播接收记录", "type": 0, "url": "http://127.0.0.1:10079/dlnam3u/tv.m3u", "ua": "okhttp/3.15.", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "FMM.m3u", "type": 0, "url": "http://127.0.0.1:10079/c/3600/proxy/https://raw.githubusercontent.com/fanmingming/live/refs/heads/main/tv/m3u/ipv6.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "FMM.m3u.音质增强", "type": 0, "url": "http://127.0.0.1:10079/mp4/3600/proxy/https://raw.githubusercontent.com/fanmingming/live/refs/heads/main/tv/m3u/ipv6.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊TX赛事直播", "type": 0, "url": "http://127.0.0.1:10079/c/60/null/http://127.0.0.1:35456/txevent.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊QY赛事直播", "type": 0, "url": "http://127.0.0.1:10079/c/60/null/http://127.0.0.1:35456/iqyevent.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊B站直播", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/bililive.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊B站直播.音质增强", "type": 0, "url": "http://127.0.0.1:10079/mp4/3600/null/http://127.0.0.1:35456/bililive.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊虎牙一起看", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/huyayqk.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊虎牙一起看.音质增强", "type": 0, "url": "http://127.0.0.1:10079/mp4/3600/null/http://127.0.0.1:35456/huyayqk.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊斗鱼一起看", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/douyuyqk.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊斗鱼一起看.音质增强", "type": 0, "url": "http://127.0.0.1:10079/mp4/3600/null/http://127.0.0.1:35456/douyuyqk.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "肥羊YY轮播", "type": 0, "url": "http://127.0.0.1:10079/c/3600/null/http://127.0.0.1:35456/yylunbo.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "YTB直播", "type": 0, "url": "./lib/ytblive.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "AKTV.电视直播", "type": 0, "url": "http://aktv.top/live.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "Gather.电视直播", "type": 0, "url": "https://tv.iill.top/m3u/Gather", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "Gather.网络直播", "type": 0, "url": "https://tv.iill.top/m3u/Live", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "FMM.txt", "type": 0, "url": "http://127.0.0.1:10079/c/3600/proxy/https://fanmingming.com/txt?url=https://live.fanmingming.cn/tv/m3u/ipv6.m3u", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "zouming.ipv6", "type": 0, "url": "http://127.0.0.1:10079/c/3600/proxy/http://ww.zouming.com/ipv6.txt", "ua": "okhttp/3.15", "epg": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/json?ch={name}&date={date}", "logo": "http://127.0.0.1:10079/p/0/proxy/https://epg.v1.mk/logo/{name}.png"}, {"name": "YTLive", "type": 0, "url": "./lib/yo21.txt"}, {"group": "redirect", "channels": [{"name": "redirect", "urls": ["proxy://do=live&type=live&proxy=&url="]}]}], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "proxy": ["raw.githubusercontent.com", "googlevideo.com", "cdn.v82u1l.com", "cdn.iz8qkg.com", "cdn.kin6c1.com", "c.biggggg.com", "c.olddddd.com", "haiwaikan.com", "www.histar.tv", "youtube.com", "uhibo.com", ".*boku.*", ".*nivod.*", ".*ulivetv.*"], "hosts": ["cache.ott.ystenlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com", "cache.ott.bestlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com", "cache.ott.wasulive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com", "cache.ott.fifalive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com", "cache.ott.hnbblive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"], "rules": [{"name": "proxy", "hosts": ["raw.githubusercontent.com", "googlevideo.com", "cdn.v82u1l.com", "cdn.iz8qkg.com", "cdn.kin6c1.com", "c.biggggg.com", "c.olddddd.com", "haiwaikan.com", "www.histar.tv", "youtube.com", "uhibo.com", ".*boku.*", ".*nivod.*", "*.t4tv.hz.cz", ".*ulivetv.*"]}, {"host": "www.djuu.com", "rule": ["mp4.djuu.com", "m4a"]}, {"host": "www.sharenice.net", "rule": ["huoshan.com", "/item/video/"], "filter": []}, {"host": "www.sharenice.net", "rule": ["sovv.qianpailive.com", "vid="], "filter": []}, {"host": "www.sharenice.net", "rule": ["douyin.com", "/play/"]}, {"host": "m.ysxs8.vip", "rule": ["ysting.ysxs8.vip:81", "xmcdn.com"], "filter": []}, {"host": "hdmoli.com", "rule": [".m3u8"]}, {"host": "https://api.live.bilibili.com", "rule": ["bilivideo.com", "/index.m3u8"], "filter": ["data.bilibili.com/log/web", "i0.hdslb.com/bfs/live/"]}, {"host": "www.agemys.cc", "rule": ["cdn-tos", "obj/tos-cn"]}, {"host": "www.fun4k.com", "rule": ["https://hd.ijycnd.com/play", "index.m3u8"]}, {"host": "zjmiao.com", "rule": ["play.videomiao.vip/API.php", "time=", "key=", "path="]}, {"name": "火山嗅探", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "抖音嗅探", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "農民嗅探", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "夜市", "hosts": ["yeslivetv.com"], "script": ["document.getElementsByClassName('vjs-big-play-button')[0].click()"]}, {"name": "毛驢", "hosts": ["www.maolvys.com"], "script": ["document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"]}, {"name": "磁力广告", "hosts": ["magnet"], "regex": ["更多", "社 區", "x u u", "最 新", "直 播", "更 新", "社 区", "有 趣", "英皇体育", "全中文AV在线", "澳门皇冠赌场", "哥哥快来", "美女荷官", "裸聊", "新片首发", "UUE29"]}, {"host": "www.iesdouyin.com", "rule": ["playwm/?video_id="]}, {"host": "www.ysgc.vip", "rule": ["getm3u8?url=http"]}, {"host": "v.douyin.com", "rule": ["playwm/?video_id="]}, {"host": "dyxs20.com", "rule": [".m3u8"]}, {"host": "www.agemys.cc", "rule": ["cdn-tos", "obj/tos-cn"]}, {"host": "www.sharenice.net", "rule": ["http.*?/play.{0,3}\\?[^url]{2,8}=.*", "qianpailive.com", "vid="]}, {"name": "暴风", "hosts": ["bfzy", "bfbfvip", "bfengbf"], "regex": ["#EXTINF.*?\\s+.*?adjump.*?\\.ts"]}, {"name": "量子", "hosts": ["vip.lz", "hd.lz", ".cdnlz"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:7\\.166667,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?08646.*?\\.ts", "17.19", "19.63"]}, {"name": "非凡", "hosts": ["vip.ffzy", "hd.ffzy", "super.ffzy", "cachem3u8.2s0"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.400000,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?1171(057).*?\\.ts", "#EXTINF.*?\\s+.*?6d7b(077).*?\\.ts", "#EXTINF.*?\\s+.*?6718a(403).*?\\.ts", "17.99", "14.45"]}, {"name": "索尼", "hosts": ["suonizy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:1\\.000000,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?p1ayer.*?\\.ts", "#EXTINF.*?\\s+.*?\\/video\\/original.*?\\.ts"]}, {"name": "快看", "hosts": ["kuaikan"], "regex": ["#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:2\\.4,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:1\\.467,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "<PERSON><PERSON><PERSON>", "hosts": ["leshiyuncdn"], "regex": ["15.92"]}, {"name": "1080zyk", "hosts": ["high24-playback", "high20-playback", "yzzy.play", "yzzy-dy"], "regex": ["16.63", "17.66"]}, {"name": "ikun", "hosts": ["b<PERSON><PERSON><PERSON><PERSON>"], "regex": ["#EXTINF.*?\\s+.*?XR8pDxQk.*?\\.ts"]}, {"name": "黑木耳hmr", "hosts": ["hmrvideo"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3\\.366667,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "ryplay", "hosts": ["cdn.ryplay"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.633333,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8\\.333333,[\\s\\S].*?\\.ts", "#EXTINF:2\\.933333,[\\s\\S].*?\\.ts"]}, {"name": "555DM", "hosts": ["cqxfjz"], "regex": ["10.56"]}, {"name": "海外看", "hosts": ["haiwaikan"], "regex": ["10.0099", "10.3333", "16.0599", "8.1748", "10.85"]}, {"name": "磁力广告", "hosts": ["magnet"], "regex": ["更多", "社 區", "x u u", "最 新", "更 新", "社 区", "有趣", "有 趣", "英皇体育", "全中文AV在线", "澳门皇冠赌场", "哥哥快来", "美女荷官", "裸聊", "新片首发", "UUE29"]}, {"name": "♻️量非", "hosts": ["lz", "vip.lz", "v.cdnlz", "hd.lz", "ffzy", "vip.ffzy", "hd.ffzy"], "regex": ["#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.600000,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "♻️索尼", "hosts": ["suonizy", "qrssv.com"], "regex": ["15.1666", "15.2666"]}, {"name": "♻️乐视", "hosts": ["<PERSON><PERSON><PERSON>"], "regex": ["15.92"]}, {"name": "♻️优质", "hosts": ["yzzy", "playback"], "regex": ["16.63", "18.66", "17.66", "19.13"]}, {"name": "♻️快看", "hosts": ["kuaikan", "vip.kuaikan"], "regex": ["15.32", "15.231", "18.066"]}, {"name": "♻️360", "hosts": ["lyhuicheng"], "regex": ["#EXTINF.*?\\s+.*?hrz8QcR9.*?\\.ts\\s+", "#EXT-X-KEY:METHOD=NONE[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "♻️开源棋牌", "hosts": ["askzycdn", "jkunbf", "b<PERSON><PERSON><PERSON><PERSON>", "bfaskcdn"], "regex": ["#EXT-X-KEY:METHOD=NONE\r*\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-KEY:METHOD=AES-128,URI=\"[^\"]+\"\r*\n*#EXTINF:3.333,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "♻️暴风", "hosts": ["bfengbf.com", "bfzy", "c1"], "regex": ["#EXTINF.*?\\s+.*?adjump.*?\\.ts\\s+", "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "♻️农民", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "♻️火山", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "♻️抖音", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "♻️磁力", "hosts": ["magnet"], "regex": ["最 新", "直 播", "更 新"]}, {"name": "♻️饭团点击", "hosts": ["<PERSON><PERSON><PERSON>", "freeok", "<PERSON><PERSON><PERSON>"], "script": ["document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();"]}, {"name": "♻️毛驴点击", "hosts": ["www.maolvys.com"], "script": ["document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"]}], "parses": [{"name": "解析聚合", "type": 3, "url": "Demo"}, {"name": "Web聚合", "type": 3, "url": "Web"}, {"name": "Json轮询", "type": 2, "url": "Sequence"}, {"name": "巧技", "type": 1, "url": "https://zy.qiaoji8.com/neibu.php?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "bilibili"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "2", "type": 1, "url": "https://json-vipjx.952707.xyz/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "bilibili"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "1", "type": 1, "url": "http://jx.dedyn.io/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "bilibili"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "3", "type": 1, "url": "http://jx.dedyn.io/jx.php?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "bilibili"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "巧技二", "type": 1, "url": "https://zy.qiaoji8.com/gouzi.php?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "bilibili", "1905", "NetFilx"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "牛牛2", "type": 1, "url": "https://zy.qiaoji8.com/xiafan.php?url=", "ext": {"flag": ["QD4K", "iyf", "<PERSON><PERSON><PERSON>", "gzcj", "GTV", "GZYS", "weggz", "Ace"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "巧技三", "type": 1, "url": "https://zy.qiaoji8.com/xiafan.php?url=", "ext": {"flag": ["QD4K", "iyf", "<PERSON><PERSON><PERSON>", "gzcj", "GTV", "GZYS", "weggz", "Ace"], "header": {"User-Agent": "okhttp/4.9.1"}}}, {"name": "-咸鱼-", "type": 0, "url": "https://jx.xymp4.cc/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.3124.68"}}}, {"name": "-虾米-", "type": 0, "url": "https://jx.xmflv.com/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.57"}}}, {"name": "-淘片-", "type": 0, "url": "https://jx.yparse.com/index.php?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"}}}, {"name": "-冰豆-", "type": 0, "url": "https://bd.jx.cn/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"}}}, {"name": "-77解析-", "type": 0, "url": "https://jx.77flv.cc/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"}}}, {"name": "-zui-", "type": 0, "url": "https://jx.zui.cm/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"}}}, {"name": "-盘古-", "type": 0, "url": "https://www.playm3u8.cn/jiexi.php?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"}}}, {"name": "-夜幕-", "type": 0, "url": "https://yemu.xyz/?url=", "ext": {"header": {"user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"}}}], "flags": ["youku", "qq", "<PERSON><PERSON><PERSON>", "qiyi", "letv", "sohu", "tudou", "pptv", "mgtv", "wasu"], "wallpaper": "http://饭太硬.top/深色壁纸/api.php", "disabled_wallpaper": "http://www.kf666888.cn/api/tvbox/img"}