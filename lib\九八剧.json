{
    "规则名": "九八剧",
    "规则作者": "香雅情",
    "请求头参数": "User-Agent$手机#Referer$http://www.98wap.com/",
    "网页编码格式": "UTF-8",
    "图片是否需要代理": "0",
    "是否开启获取首页数据": "1",
    //首页推荐数据获取链接
    "首页推荐链接": "http://www.98wap.com",
    //首页推荐列表数组截取。
    "首页列表数组规则": "body&&.stui-vodlist:has(h4)",
    //首页推荐片单列表数组定位。
    "首页片单列表数组规则": "li",
    //首页推荐片单信息jsoup与正则截取写法切换，只作用于html网页，1为jsoup写法(默认)，0为正则截取写法
    "首页片单是否Jsoup写法":"1",
    //首页片单标题
    "首页片单标题": "h4&&a&&Text",
    //首页推荐片单链接
    "首页片单链接": "h4&&a&&href",
    //首页推荐片单图片，支持自定义图片链接
    "首页片单图片": ".lazyload&&data-original",
    //首页推荐片单副标题
    "首页片单副标题":".pic-text&&Text",
    //首页推荐片单链接补前缀  
    "首页片单链接加前缀": "http://www.98wap.com",
    //首页推荐片单链接补后缀
    "首页片单链接加后缀": "",
    
    "分类起始页码": "1",
    "分类链接": "http://www.98wap.com/{cateId}/index{catePg}.html[firstPage=http://www.98wap.com/{cateId}/index.html]",
    "分类名称": "电影&电视剧&综艺&动漫",
    "分类名称替换词": "dianyingpian&dianshiju&zongyi&dongman",
    "筛选数据": "ext",
    //{cateId}
    "筛选子分类名称": "动作片&爱情片&科幻片&恐怖片&战争片&喜剧片&纪录片&剧情片||国产剧&港台剧&欧美剧&日韩剧&短剧",
    "筛选子分类替换词": "dongzuopian&aiqingpian&kehuanpian&kongbupian&zhanzhengpian&xijupian&jilupian&juqingpian||guocanju&gangtaiju&oumeiju&rihanju&duanju",
    "分类截取模式": "1",
    "分类列表数组规则": ".stui-vodlist&&li",
    "分类片单是否Jsoup写法": "1",
    "分类片单标题": "h4&&a&&Text",
    "分类片单链接": "h4&&a&&href",
    "分类片单图片": ".lazyload&&data-original",
    "分类片单副标题": ".pic-text&&Text",
    "分类片单链接加前缀": "http://www.98wap.com",
    "分类片单链接加后缀": "",
    "搜索请求头参数": "User-Agent$手机#Referer$http://www.98wap.com/",
    "搜索链接": "http://www.98wap.com/search.php;post",
    "POST请求数据": "searchword={wd}",
    "搜索截取模式": "1",
    "搜索列表数组规则": ".stui-vodlist__media&&li",
    "搜索片单是否Jsoup写法": "1",
    "搜索片单图片": ".lazyload&&data-original",
    "搜索片单标题": "h3&&a&&Text",
    "搜索片单链接": "h3&&a&&href",
    "搜索片单副标题": ".pic-text&&Text",
    "搜索片单链接加前缀": "http://www.98wap.com",
    "搜索片单链接加后缀": "",
    "链接是否直接播放": "0",
    "直接播放链接加前缀": "https://live.52sf.ga/huya/",
    "直接播放链接加后缀": "#isVideo=true#",
    "直接播放直链视频请求头": "authority$ku.peizq.online#Referer$https://play.peizq.online",
    "详情是否Jsoup写法": "0",
    "类型详情": "类型：</span>&&</a",
    "年代详情": "",
    "地区详情": "",
    "演员详情": "主演：</span>&&</p>",
    "简介详情": "简介：</span>&&</p>",
    "线路列表数组规则": "body&&#playlist",
    "线路标题": "h3&&Text",
    "播放列表数组规则": "body&&.stui-content__playlist",
    "选集列表数组规则": "li",
    "选集标题链接是否Jsoup写法": "1",
    "选集标题": "a&&Text",
    "选集链接": "a&&href",
    "是否反转选集序列": "0",
    "选集链接加前缀": "http://www.98wap.com",
    "选集链接加后缀": "",
    "分析MacPlayer": "0",
    "是否开启手动嗅探": "1",
    "手动嗅探视频链接关键词": ".mp4#.m3u8#.flv#video/tos#obj/tos-#pt=m3u8#mime_type=video",
    "手动嗅探视频链接过滤词": ".html#=http"
}