{"author": "claude", "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36", "homeUrl": "https://www.4kvm.tv/", "dcVipFlag": "true", "dcPlayUrl": "true", "cateManual": {"电影": "movies", "电视剧": "tvshows", "高分电影": "imdb", "热门播放": "trending"}, "homeVodNode": "//div[contains(@class,'items')]/div[contains(@class,'item')]", "homeVodName": "/div[contains(@class,'data')]/h3/a/text()", "homeVodId": "/div[contains(@class,'data')]/h3/a/@href", "homeVodIdR": "/(\\w+)/$", "homeVodImg": "/div[contains(@class,'poster')]/img/@src", "homeVodImgR": "", "homeVodMark": "/div[contains(@class,'data')]/div[contains(@class,'meta')]/text()", "cateUrl": "https://www.4kvm.tv/{cateId}/page/{catePg}", "cateVodNode": "//div[contains(@class,'items')]/div[contains(@class,'item')]", "cateVodName": "/div[contains(@class,'data')]/h3/a/text()", "cateVodId": "/div[contains(@class,'data')]/h3/a/@href", "cateVodIdR": "/(\\w+)/$", "cateVodImg": "/div[contains(@class,'poster')]/img/@src", "cateVodImgR": "", "cateVodMark": "/div[contains(@class,'data')]/div[contains(@class,'meta')]/text()", "dtUrl": "https://www.4kvm.tv/{vid}/", "dtNode": "//div[contains(@class,'sheader')]", "dtName": "//div[contains(@class,'data')]/h1/text()", "dtNameR": "", "dtImg": "//div[contains(@class,'poster')]/img/@src", "dtImgR": "", "dtCate": "//div[contains(@class,'sgeneros')]/a/text()", "dtCateR": "", "dtYear": "//div[contains(@class,'data')]/div[contains(@class,'extra')]/span[2]/text()", "dtYearR": "", "dtArea": "", "dtAreaR": "", "dtMark": "//div[contains(@class,'data')]/div[contains(@class,'extra')]/span[1]/text()", "dtMarkR": "", "dtActor": "//div[contains(@class,'persons')]/div[contains(@class,'person')]/div[contains(@class,'data')]/div[contains(@class,'name')]/text()", "dtActorR": "", "dtDirector": "//div[contains(@class,'person')][.//div[contains(text(), '导演')]]/div[contains(@class,'data')]/div[contains(@class,'name')]/text()", "dtDirectorR": "", "dtDesc": "//div[contains(@id,'info')]/div[contains(@class,'wp-content')]/p/text()", "dtDescR": "", "dtFromNode": "//div[contains(@class,'tabs')]/ul/li", "dtFromName": "/a/text()", "dtFromNameR": "", "dtUrlNode": "//div[contains(@class,'se-c')]", "dtUrlSubNode": "//ul[contains(@class,'episodios')]/li", "dtUrlId": "/a/@href", "dtUrlIdR": "/episodes/(\\S+)", "dtUrlName": "/div[contains(@class,'episodiotitle')]/a/text()", "dtUrlNameR": "", "playUrl": "https://www.4kvm.tv/episodes/{playUrl}", "playUa": "", "searchUrl": "https://www.4kvm.tv/xssearch?s={wd}", "scVodNode": "//div[contains(@class,'result-item')]", "scVodName": "//div[contains(@class,'title')]/a/text()", "scVodId": "//div[contains(@class,'title')]/a/@href", "scVodIdR": "/(\\w+)/$", "scVodImg": "//div[contains(@class,'image')]/img/@src", "scVodMark": "//div[contains(@class,'meta')]/text()", "filter": {"movies": [{"key": "year", "name": "年份", "value": [{"n": "全部", "v": ""}, {"n": "2025", "v": "/release/2025"}, {"n": "2024", "v": "/release/2024"}, {"n": "2023", "v": "/release/2023"}, {"n": "2022", "v": "/release/2022"}, {"n": "2021", "v": "/release/2021"}, {"n": "2020", "v": "/release/2020"}, {"n": "2019", "v": "/release/2019"}, {"n": "2018", "v": "/release/2018"}, {"n": "2017", "v": "/release/2017"}, {"n": "2016", "v": "/release/2016"}, {"n": "2015", "v": "/release/2015"}, {"n": "2014", "v": "/release/2014"}, {"n": "2013", "v": "/release/2013"}, {"n": "2012", "v": "/release/2012"}, {"n": "2011", "v": "/release/2011"}, {"n": "2010", "v": "/release/2010"}]}], "tvshows": [{"key": "year", "name": "年份", "value": [{"n": "全部", "v": ""}, {"n": "2025", "v": "/release/2025"}, {"n": "2024", "v": "/release/2024"}, {"n": "2023", "v": "/release/2023"}, {"n": "2022", "v": "/release/2022"}, {"n": "2021", "v": "/release/2021"}, {"n": "2020", "v": "/release/2020"}, {"n": "2019", "v": "/release/2019"}, {"n": "2018", "v": "/release/2018"}, {"n": "2017", "v": "/release/2017"}, {"n": "2016", "v": "/release/2016"}, {"n": "2015", "v": "/release/2015"}]}]}}