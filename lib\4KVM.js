const rule = {
    // 基础配置
    type: "影视",
    title: "4K影视",
    description: "源动力出品",
    baseUrl: "https://www.4kvm.org",
    path: "/fyclass/page/fypage",
    searchPath: "/xssearch?s=**&f=_all&p=fypage",

    // 请求配置
    headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    },
    timeout: 15000,
    playParse: true,

    // 分类映射
    categories: {
        movies: "电影",
        tvshows: "电视剧",
        seasons: "季播剧"
    },

    // 核心方法
    async fetchPage(url) {
        try {
            const response = await request(url);
            return response.data;
        } catch (error) {
            console.error(`请求失败: ${url}`, error);
            return null;
        }
    },

    // 数据解析通用方法
    parseItems(selector, fields) {
        return pdfa(html, selector).map(item => {
            return fields.reduce((acc, field) => {
                acc[field.name] = pdfh(item, field.selector);
                return acc;
            }, {});
        });
    },

    // 推荐页处理
    async getRecommendations(page = 1) {
        const html = await this.fetchPage(`${this.baseUrl}${this.path}?page=${page}`);
        return this.parseItems(".content .items .item", [{
                name: "title",
                selector: "article .data h3 a&&Text"
            },
            {
                name: "poster",
                selector: "article .poster img&&src"
            },
            {
                name: "description",
                selector: "article .data span&&Text"
            },
            {
                name: "url",
                selector: "article .data h3 a&&href",
                process: url => url.replace(this.baseUrl, "")
            }
        ]);
    },

    // 分类页处理
    async getCategoryContent(page = 1) {
        const html = await this.fetchPage(`${this.baseUrl}${this.path}?page=${page}`);
        return this.parseItems(".animation-2 .item", [{
                name: "title",
                selector: "article .data h3 a&&Text"
            },
            {
                name: "poster",
                selector: "article .poster img&&src"
            },
            {
                name: "description",
                selector: "article .data span&&Text"
            },
            {
                name: "url",
                selector: "article .data h3 a&&href",
                process: url => url.replace(this.baseUrl, "")
            }
        ]);
    },

    // 详情页解析
    async parseDetail(html) {
        const $ = cheerio.load(html);
        const videoData = {};

        // 提取基础信息
        videoData.id = this.extractId(html);
        videoData.title = pdfh(html, ".sheader .data h1&&Text");
        videoData.poster = pdfh(html, ".sheader .poster img&&src");
        videoData.description = pdfh(html, "#info .wp-content&&Text");

        // 处理播放信息
        videoData.playSources = this.extractPlaySources(html);
        videoData.metadata = {
            director: this.extractMetadata(html, ".persons .person .data .name a&&Text"),
            actors: this.extractMetadata(html, ".persons .person meta&&content"),
            genres: this.extractMetadata(html, ".sheader .data .sgeneros a&&Text"),
            year: pdfh(html, ".sheader .data .extra .date&&Text")
        };

        return videoData;
    },

    // 播放地址解析
    extractPlaySources(html) {
        const scriptContent = pdfa(html, "body script")
            .find(s => s.includes("Artplayer"))[0]
            .match(/url:'(.*?)'/g) || [];

        return scriptContent.map(url => {
            if (/\.m3u8$/.test(url)) return {
                type: "hls",
                url
            };
            if (/\.mp4$/.test(url)) return {
                type: "mp4",
                url
            };
            return {
                type: "unknown",
                url
            };
        });
    },

    // 搜索功能
    async search(keyword, page = 1) {
        const html = await this.fetchPage(`/search?s=${encodeURIComponent(keyword)}&page=${page}`);
        return this.parseItems(".search-page .result-item", [{
                name: "title",
                selector: "article .details .title a&&Text"
            },
            {
                name: "poster",
                selector: "article .image .thumbnail a img&&src"
            },
            {
                name: "description",
                selector: "article .image .thumbnail a span&&Text"
            },
            {
                name: "url",
                selector: "article .details .title a&&href",
                process: url => url.replace(this.baseUrl, "")
            }
        ]);
    }
};

// 使用示例
(async () => {
    const detailPage = await rule.parseDetail(await rule.fetchPage("https://www.4kvm.org/movie/12345"));
    console.log(detailPage);
})();