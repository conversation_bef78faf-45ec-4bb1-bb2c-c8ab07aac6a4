# -*- coding: utf-8 -*-
# NKDVD爬虫 - https://www.nkdvd.me/
# 作者: AI Assistant
# 说明: 基于PyramidStore框架的影视爬虫，支持完整的解密功能

import sys
import re
import json
import urllib.parse
import os
import base64
import hashlib

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from base.spider import Spider


class Spider(Spider):

    def init(self, extend=""):
        self.host = 'https://www.nkdvd.me'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def getName(self):
        return "NKDVD"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def homeVideoContent(self):
        """分类定义 - TVBox兼容"""
        try:
            result = {
                'class': [
                    {'type_name': '电影', 'type_id': '20'},
                    {'type_name': '剧集', 'type_id': '21'},
                    {'type_name': '动漫', 'type_id': '22'},
                    {'type_name': '综艺', 'type_id': '23'},
                    {'type_name': '纪录片', 'type_id': '24'},
                    {'type_name': '爽剧', 'type_id': '25'},
                ]
            }
            return result
        except Exception as e:
            self.log(f"分类定义出错: {str(e)}")
            return {'class': []}

    def _extract_video_info(self, element):
        """提取视频信息"""
        try:
            # 获取视频链接和ID
            link_elem = element.xpath('.//a[contains(@href,"/video/")]/@href')
            if not link_elem:
                return None

            link = link_elem[0]
            vod_id = self.regStr(r'/video/(\d+)\.html', link)
            if not vod_id:
                return None

            # 获取标题
            title_selectors = [
                './/a[contains(@href,"/video/")]/@title',
                './/a[contains(@href,"/video/")]/text()',
                './/h3//text()',
                './/h4//text()',
                './/div[@class="video-name"]/text()',
                './/span[@class="video-name"]/text()'
            ]

            title = ''
            for title_selector in title_selectors:
                title_elem = element.xpath(title_selector)
                if title_elem:
                    title = title_elem[0].strip()
                    if title and len(title) > 1:
                        break

            # 获取封面图片
            pic_elem = element.xpath('.//img/@data-src | .//img/@src | .//img/@data-original')
            pic = ''
            for pic_url in pic_elem:
                if pic_url and not pic_url.startswith('data:') and 'load' not in pic_url:
                    pic = pic_url
                    if not pic.startswith('http'):
                        pic = self.host + pic
                    break

            # 获取更新状态
            remarks_selectors = [
                './/span[contains(@class,"pic-text")]/text()',
                './/div[contains(@class,"pic-text")]/text()',
                './/span[contains(text(),"更新")]/text()',
                './/span[contains(text(),"集")]/text()',
                './/span[contains(text(),"完结")]/text()',
                './/div[contains(@class,"video-serial")]/text()',
                './/span[contains(@class,"video-serial")]/text()'
            ]

            remarks = ''
            for remarks_selector in remarks_selectors:
                remarks_elem = element.xpath(remarks_selector)
                if remarks_elem:
                    remarks = remarks_elem[0].strip()
                    if remarks:
                        break

            if title and vod_id:
                return {
                    'vod_id': vod_id,
                    'vod_name': title,
                    'vod_pic': pic,
                    'vod_remarks': remarks,
                    'vod_year': ''
                }
        except Exception as e:
            self.log(f"解析视频信息出错: {str(e)}")
        return None

    def homeContent(self, filter):
        """获取首页内容和分类定义"""
        result = {}

        # 定义分类 - 符合TVBox标准
        classes = [
            {'type_name': '电影', 'type_id': '20'},
            {'type_name': '剧集', 'type_id': '21'},
            {'type_name': '动漫', 'type_id': '22'},
            {'type_name': '综艺', 'type_id': '23'},
            {'type_name': '纪录片', 'type_id': '24'},
            {'type_name': '爽剧', 'type_id': '25'},
        ]
        result['class'] = classes

        # 获取首页推荐内容 - 修复版
        try:
            rsp = self.fetch(self.host, headers=self.headers)
            content = rsp.text
            doc = self.html(content)

            videos = []

            # 基于实际网站结构的提取策略
            # 提取视频卡片，包含真实图片
            card_pattern = r'<a[^>]*href="(/video/(\d+)\.html)"[^>]*title="([^"]*)"[^>]*class="module-poster-item[^"]*"[^>]*>(.*?)</a>'
            matches = re.findall(card_pattern, content, re.DOTALL)

            self.log(f"首页找到视频卡片: {len(matches)} 个")

            for match in matches:
                video_url, video_id, title, card_content = match
                title = title.strip()

                # 过滤无效标题
                if not title or title in ['首页', '电影', '剧集', '动漫', '综艺', '纪录片', '爽剧']:
                    continue

                # 提取真实图片URL（从data-original属性）
                pic_url = ""
                pic_match = re.search(r'data-original="([^"]*)"', card_content)
                if pic_match:
                    pic_url = pic_match.group(1)
                else:
                    # 备用：从src属性提取
                    pic_match = re.search(r'<img[^>]*src="([^"]*)"', card_content)
                    if pic_match:
                        pic_url = pic_match.group(1)

                # 提取状态信息（如"已完结"）
                status = ""
                status_match = re.search(r'<div[^>]*class="module-item-note"[^>]*>([^<]*)</div>', card_content)
                if status_match:
                    status = status_match.group(1).strip()

                videos.append({
                    'vod_id': video_id,
                    'vod_name': title,
                    'vod_pic': pic_url,
                    'vod_remarks': status or '推荐',
                    'vod_year': ''
                })

            # 去重
            videos = self._remove_duplicates(videos)

            # 限制数量
            if len(videos) > 20:
                videos = videos[:20]

            self.log(f"首页最终获取到视频: {len(videos)} 个")
            result['list'] = videos

        except Exception as e:
            self.log(f"获取首页内容出错: {str(e)}")
            result['list'] = []

        return result

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类内容 - 修复版"""
        result = {}
        videos = []

        try:
            # 构建分类页面URL
            if pg == '1':
                url = f"{self.host}/type/{tid}.html"
            else:
                url = f"{self.host}/type/{tid}-{pg}.html"

            self.log(f"分类页面URL: {url}")

            rsp = self.fetch(url, headers=self.headers)
            content = rsp.text

            # 基于实际网站结构的分类视频提取
            # 提取视频卡片，包含真实图片
            card_pattern = r'<a[^>]*href="(/video/(\d+)\.html)"[^>]*title="([^"]*)"[^>]*class="module-poster-item[^"]*"[^>]*>(.*?)</a>'
            matches = re.findall(card_pattern, content, re.DOTALL)

            self.log(f"分类页找到视频卡片: {len(matches)} 个")

            for match in matches:
                video_url, video_id, title, card_content = match
                title = title.strip()

                # 过滤无效标题
                if not title or len(title) < 2:
                    continue

                # 提取真实图片URL（从data-original属性）
                pic_url = ""
                pic_match = re.search(r'data-original="([^"]*)"', card_content)
                if pic_match:
                    pic_url = pic_match.group(1)
                else:
                    # 备用：从src属性提取
                    pic_match = re.search(r'<img[^>]*src="([^"]*)"', card_content)
                    if pic_match:
                        pic_url = pic_match.group(1)

                # 提取状态信息（如"已完结"）
                status = ""
                status_match = re.search(r'<div[^>]*class="module-item-note"[^>]*>([^<]*)</div>', card_content)
                if status_match:
                    status = status_match.group(1).strip()

                videos.append({
                    'vod_id': video_id,
                    'vod_name': title,
                    'vod_pic': pic_url,
                    'vod_remarks': status,
                    'vod_year': ''
                })

            # 去重处理
            videos = self._remove_duplicates(videos)

            self.log(f"分类页最终获取到视频: {len(videos)} 个")

            result['list'] = videos
            result['page'] = pg
            result['pagecount'] = 999
            result['limit'] = 20
            result['total'] = 999 * 20

        except Exception as e:
            self.log(f"获取分类内容出错: {str(e)}")
            result['list'] = []
            result['page'] = pg
            result['pagecount'] = 1
            result['limit'] = 20
            result['total'] = 0

        return result

    def searchContent(self, key, quick, pg="1"):
        """搜索内容 - 多策略绕过验证码"""
        result = {}
        videos = []

        try:
            self.log(f"开始搜索: {key}")

            # 策略1: 通过分类页面模糊搜索
            videos.extend(self._search_by_categories(key))

            # 策略2: 尝试不同的搜索接口
            if not videos:
                videos.extend(self._search_by_api(key))

            # 策略3: 通过首页推荐内容搜索
            if not videos:
                videos.extend(self._search_by_homepage(key))

            # 策略4: 最后尝试直接搜索（可能遇到验证码）
            if not videos:
                videos.extend(self._search_direct(key))

            # 去重和过滤
            videos = self._remove_duplicates(videos)
            videos = [v for v in videos if key.lower() in v['vod_name'].lower()]

            self.log(f"搜索完成，找到 {len(videos)} 个结果")
            result['list'] = videos

        except Exception as e:
            self.log(f"搜索出错: {str(e)}")
            result['list'] = []

        return result

    def _search_by_categories(self, key):
        """策略1: 通过分类页面搜索"""
        videos = []
        try:
            self.log(f"策略1: 通过分类页面搜索 {key}")

            # 搜索所有分类的前几页
            categories = ['20', '21', '22', '23', '24', '25']

            for category_id in categories:
                for page in ['1', '2']:  # 只搜索前2页
                    try:
                        category_result = self.categoryContent(category_id, page, {}, {})
                        if category_result and 'list' in category_result:
                            category_videos = category_result['list']

                            # 筛选包含关键词的视频
                            for video in category_videos:
                                if key.lower() in video['vod_name'].lower():
                                    video['vod_remarks'] = '分类搜索'
                                    videos.append(video)

                        # 限制搜索结果数量，避免过多请求
                        if len(videos) >= 20:
                            break

                    except Exception as e:
                        continue

                if len(videos) >= 20:
                    break

            self.log(f"分类搜索找到 {len(videos)} 个结果")

        except Exception as e:
            self.log(f"分类搜索失败: {str(e)}")

        return videos

    def _search_by_api(self, key):
        """策略2: 尝试API搜索接口"""
        videos = []
        try:
            self.log(f"策略2: 尝试API搜索 {key}")

            # 尝试可能的API接口
            api_urls = [
                f"{self.host}/api/search",
                f"{self.host}/search/api",
                f"{self.host}/api.php/search",
                f"{self.host}/index.php/api/search"
            ]

            for api_url in api_urls:
                try:
                    # POST请求
                    data = {'keyword': key, 'wd': key, 'q': key}
                    rsp = self.post(api_url, data=data, headers=self.headers)

                    if rsp.status_code == 200:
                        try:
                            json_data = json.loads(rsp.text)
                            if 'data' in json_data or 'list' in json_data:
                                self.log(f"API搜索成功: {api_url}")
                                # 解析API返回的数据
                                # 这里需要根据实际API格式调整
                                break
                        except:
                            pass

                    # GET请求
                    params = {'keyword': key, 'wd': key, 'q': key}
                    rsp = self.fetch(api_url, params=params, headers=self.headers)

                    if rsp.status_code == 200 and 'video' in rsp.text:
                        self.log(f"API搜索可能成功: {api_url}")
                        # 解析返回内容
                        break

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"API搜索失败: {str(e)}")

        return videos

    def _search_by_homepage(self, key):
        """策略3: 通过首页内容搜索"""
        videos = []
        try:
            self.log(f"策略3: 通过首页内容搜索 {key}")

            # 获取首页内容
            home_result = self.homeContent({})
            if home_result and 'list' in home_result:
                home_videos = home_result['list']

                # 筛选包含关键词的视频
                for video in home_videos:
                    if key.lower() in video['vod_name'].lower():
                        video['vod_remarks'] = '首页搜索'
                        videos.append(video)

            self.log(f"首页搜索找到 {len(videos)} 个结果")

        except Exception as e:
            self.log(f"首页搜索失败: {str(e)}")

        return videos

    def _search_direct(self, key):
        """策略4: 直接搜索（可能遇到验证码）"""
        videos = []
        try:
            self.log(f"策略4: 直接搜索 {key}")

            # 使用更隐蔽的请求头
            search_headers = self.headers.copy()
            search_headers.update({
                'Referer': self.host,
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/javascript, */*; q=0.01'
            })

            # 尝试不同的搜索URL格式
            search_urls = [
                f"{self.host}/vodsearch/{urllib.parse.quote(key)}.html",
                f"{self.host}/search/{urllib.parse.quote(key)}.html",
                f"{self.host}/s/{urllib.parse.quote(key)}.html"
            ]

            for search_url in search_urls:
                try:
                    rsp = self.fetch(search_url, headers=search_headers)
                    content = rsp.text

                    # 检查是否遇到验证码
                    if '验证码' in content or 'captcha' in content.lower() or 'verify' in content.lower():
                        self.log(f"遇到验证码，跳过: {search_url}")
                        continue

                    if '/video/' in content:
                        # 解析搜索结果
                        patterns = [
                            r'<a[^>]*href="(/video/(\d+)\.html)"[^>]*title="([^"]*)"',
                            r'href="(/video/(\d+)\.html)"[^>]*>([^<]+)</a>'
                        ]

                        for pattern in patterns:
                            matches = re.findall(pattern, content)

                            for match in matches:
                                if len(match) == 3:
                                    video_url, video_id, title = match
                                    title = title.strip()

                                    if key.lower() in title.lower():
                                        videos.append({
                                            'vod_id': video_id,
                                            'vod_name': title,
                                            'vod_pic': '',
                                            'vod_remarks': '直接搜索',
                                            'vod_year': ''
                                        })

                            if videos:
                                break

                        if videos:
                            break

                except Exception as e:
                    continue

            self.log(f"直接搜索找到 {len(videos)} 个结果")

        except Exception as e:
            self.log(f"直接搜索失败: {str(e)}")

        return videos

    def _remove_duplicates(self, videos):
        """去除重复的视频"""
        seen_ids = set()
        unique_videos = []

        for video in videos:
            vod_id = video.get('vod_id')
            if vod_id and vod_id not in seen_ids:
                seen_ids.add(vod_id)
                unique_videos.append(video)

        return unique_videos

    def detailContent(self, ids):
        """获取详情内容 - TVBox兼容版"""
        try:
            # TVBox通常只传递一个ID，按照厂长资源的模式处理
            vod_id = ids[0] if ids else None
            if not vod_id:
                return {'list': []}

            self.log(f"获取详情页: {vod_id}")

            detail_url = f"{self.host}/video/{vod_id}.html"
            rsp = self.fetch(detail_url, headers=self.headers)
            content = rsp.text
            doc = self.html(content)

            # 获取基本信息
            title_selectors = [
                '//h1/text()',
                '//div[@class="video-info-title"]/h1/text()',
                '//h3/text()',
                '//title/text()',
                '//div[contains(@class,"title")]/text()'
            ]

            title = ''
            for selector in title_selectors:
                title_elem = doc.xpath(selector)
                if title_elem:
                    title = title_elem[0].strip()
                    # 清理标题中的网站名称
                    if '耐看点播' in title:
                        title = title.split(' - ')[0].strip()
                    if title and len(title) > 1:
                        break

            # 获取封面图片
            pic_selectors = [
                '//div[@class="video-info-pic"]//img/@src',
                '//img[@class="lazyload"]/@data-src',
                '//img/@data-src',
                '//img/@src'
            ]

            pic = ''
            for selector in pic_selectors:
                pic_elem = doc.xpath(selector)
                if pic_elem:
                    for pic_url in pic_elem:
                        if pic_url and not pic_url.startswith('data:') and 'load' not in pic_url:
                            pic = pic_url
                            if not pic.startswith('http'):
                                pic = self.host + pic
                            break
                    if pic:
                        break

            # 获取描述信息
            desc_selectors = [
                '//div[@class="video-info-content"]/text()',
                '//div[contains(@class,"desc")]/text()',
                '//div[contains(@class,"content")]/text()',
                '//p[contains(@class,"desc")]/text()'
            ]

            desc = ''
            for selector in desc_selectors:
                desc_elem = doc.xpath(selector)
                if desc_elem:
                    desc = desc_elem[0].strip()
                    if desc and len(desc) > 10:
                        break

            # 获取演员、导演等信息
            director = ''
            actor = ''
            year = ''
            area = ''

            # 从页面文本中提取信息
            page_text = doc.xpath('//text()')
            all_text = ' '.join([text.strip() for text in page_text if text.strip()])

            # 提取导演信息
            director_patterns = [
                r'导演[：:]\s*([^主演年份地区语言类型更新简介\n]+)',
                r'导演[：:]\s*([^\n]+)',
                r'导演\s*([^主演年份地区语言类型更新简介\n]+)',
            ]
            for pattern in director_patterns:
                match = re.search(pattern, all_text)
                if match:
                    director = match.group(1).strip()
                    director = re.sub(r'[,，]\s*$', '', director)
                    director = re.sub(r'\s+', ' ', director)
                    if director and len(director) > 1:
                        break

            # 提取演员信息
            actor_patterns = [
                r'主演[：:]\s*([^导演年份地区语言类型更新简介\n]+)',
                r'演员[：:]\s*([^导演年份地区语言类型更新简介\n]+)',
                r'主演\s*([^导演年份地区语言类型更新简介\n]+)',
            ]
            for pattern in actor_patterns:
                match = re.search(pattern, all_text)
                if match:
                    actor = match.group(1).strip()
                    actor = re.sub(r'[,，]\s*$', '', actor)
                    actor = re.sub(r'\s+', ' ', actor)
                    if actor and len(actor) > 1:
                        break

            # 提取年份信息
            year_patterns = [
                r'年份[：:]\s*(\d{4})',
                r'(\d{4})年',
                r'上映[：:]\s*(\d{4})',
            ]
            for pattern in year_patterns:
                match = re.search(pattern, all_text)
                if match:
                    year = match.group(1)
                    break

            # 提取地区信息
            area_patterns = [
                r'地区[：:]\s*([^导演主演年份语言类型更新简介\n]+)',
                r'(美国|中国|日本|韩国|英国|法国|德国|意大利|加拿大|澳大利亚|大陆|香港|台湾|泰国|印度)',
            ]
            for pattern in area_patterns:
                match = re.search(pattern, all_text)
                if match:
                    area = match.group(1).strip()
                    if area and len(area) > 1:
                        break

            # 获取播放列表
            play_from, play_url = self._parse_play_sources(content, vod_id)

            # 调试信息
            self.log(f"视频 {vod_id} 播放源解析结果:")
            self.log(f"  播放源数量: {len(play_from)}")
            self.log(f"  播放URL数量: {len(play_url)}")
            if play_from:
                self.log(f"  播放源列表: {play_from}")

            # 确保播放源不为空
            if not play_from or not play_url:
                self.log(f"警告: 视频 {vod_id} 没有找到播放源，使用默认播放源")
                play_from = ['默认播放源']
                play_url = [f'第1集$/player/{vod_id}-1-1.html']

            video_info = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': '',
                'vod_year': year,
                'vod_area': area,
                'vod_director': director,
                'vod_actor': actor,
                'vod_content': desc,
                'type_name': '',  # TVBox需要的字段
                'vod_play_from': '$$$'.join(play_from),
                'vod_play_url': '$$$'.join(play_url)
            }

            # 按照厂长资源的模式，直接返回单个视频的详情
            return {'list': [video_info]}

        except Exception as e:
            self.log(f"获取详情出错: {str(e)}")
            return {'list': []}

    def _parse_play_sources(self, content, vod_id):
        """解析播放源"""
        play_from = []
        play_url = []

        try:
            # 查找所有播放链接，按播放源分组
            player_links = re.findall(r'/player/[^"\'>\s]+', content)
            self.log(f"找到播放链接: {len(player_links)} 个")

            if player_links:
                # 去重播放链接
                unique_links = list(set(player_links))
                self.log(f"去重后播放链接: {len(unique_links)} 个")

                # 按播放源ID分组
                sources = {}
                for player_link in unique_links:
                    episode_match = re.search(r'player/(\d+)-(\d+)-(\d+)', player_link)
                    if episode_match:
                        video_id, source_id, episode_num = episode_match.groups()

                        if source_id not in sources:
                            sources[source_id] = []

                        # 检查是否已存在相同集数
                        existing_episodes = [ep[0] for ep in sources[source_id]]
                        if int(episode_num) not in existing_episodes:
                            sources[source_id].append((int(episode_num), player_link))

                # 为每个播放源创建播放列表 - TVBox兼容格式
                source_names = {
                    '1': 'RY',
                    '2': '自建B',
                    '3': '自建A',
                    '4': 'HME',
                    '5': 'LZ',
                    '6': 'YZ',
                    '7': 'BF',
                    '8': 'ZL',
                    '9': 'FF',
                    '10': 'WW超清',
                    '11': '自建C',
                    '12': '360'
                }

                # 按播放源质量排序
                def get_source_priority(source_id):
                    priority_map = {
                        '10': 1,  # WW超清 - 最高优先级
                        '8': 2,   # ZL
                        '3': 3,   # 自建A
                        '11': 4,  # 自建C
                        '2': 5,   # 自建B
                        '1': 6,   # RY
                        '6': 7,   # YZ
                        '9': 8,   # FF
                        '7': 9,   # BF
                        '5': 10,  # LZ
                        '4': 11,  # HME
                        '12': 12  # 360
                    }
                    return priority_map.get(source_id, 99)

                # 排序播放源
                sorted_sources = sorted(sources.items(), key=lambda x: get_source_priority(x[0]))

                for source_id, episodes in sorted_sources:
                    if episodes:
                        # 按集数排序
                        episodes.sort(key=lambda x: x[0])

                        # 构建TVBox兼容的播放链接格式
                        episode_list = []
                        for episode_num, player_link in episodes:
                            # 确保播放链接格式正确
                            episode_list.append(f"第{episode_num}集${player_link}")

                        source_name = source_names.get(source_id, f'播放源{source_id}')
                        play_from.append(source_name)
                        play_url.append('#'.join(episode_list))

        except Exception as e:
            self.log(f"解析播放源出错: {str(e)}")

        return play_from, play_url

    def playerContent(self, flag, id, vipFlags):
        """播放内容 - 修复版"""
        result = {}

        try:
            self.log(f"获取播放链接: {id}")

            # 获取播放页面
            rsp = self.fetch(id, headers=self.headers)
            content = rsp.text

            if rsp.status_code != 200:
                result = {'parse': 0, 'playUrl': '', 'url': ''}
                return result

            # 提取player_aaaa配置
            player_match = re.search(r'var\s+player_aaaa\s*=\s*(\{.*?\});', content, re.DOTALL)
            if not player_match:
                self.log("未找到player_aaaa配置")
                result = {'parse': 0, 'playUrl': '', 'url': ''}
                return result

            config_json = player_match.group(1)
            self.log(f"找到player_aaaa配置")

            # 提取url字段
            url_match = re.search(r'"url"\s*:\s*"([^"\\]*(?:\\.[^"\\]*)*)"', config_json)
            if url_match:
                encrypted_vid = url_match.group(1).replace('\\/', '/')
                self.log(f"提取到加密vid: {encrypted_vid[:50]}...")

                # 调用API并解密
                real_url = self._get_real_play_url(encrypted_vid)
                if real_url:
                    result = {
                        'parse': 0,
                        'playUrl': '',
                        'url': real_url
                    }
                    self.log(f"解密成功: {real_url[:100]}...")
                    return result

            # 尝试url_next字段
            url_next_match = re.search(r'"url_next"\s*:\s*"([^"\\]*(?:\\.[^"\\]*)*)"', config_json)
            if url_next_match:
                encrypted_vid = url_next_match.group(1).replace('\\/', '/')
                self.log(f"提取到url_next字段: {encrypted_vid[:50]}...")

                # 调用API并解密
                real_url = self._get_real_play_url(encrypted_vid)
                if real_url:
                    result = {
                        'parse': 0,
                        'playUrl': '',
                        'url': real_url
                    }
                    self.log(f"解密成功: {real_url[:100]}...")
                    return result

            self.log("获取播放链接失败")
            result = {'parse': 0, 'playUrl': '', 'url': ''}

        except Exception as e:
            self.log(f"播放解析失败: {str(e)}")
            result = {'parse': 0, 'playUrl': '', 'url': ''}

        return result

    def _get_real_play_url(self, encrypted_vid):
        """获取真实播放链接"""
        try:
            # 设置API请求头
            api_headers = self.headers.copy()
            api_headers.update({
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': self.host,
                'Origin': self.host,
            })

            # 调用API
            api_url = f"{self.host}/player/api.php"
            data = {'vid': encrypted_vid}

            rsp = self.post(api_url, data=data, headers=api_headers)

            if rsp.status_code == 200:
                json_data = json.loads(rsp.text)

                if json_data.get('code') == 200 and 'data' in json_data:
                    data_obj = json_data['data']

                    if 'url' in data_obj:
                        encrypted_url = data_obj['url']

                        # 解密URL
                        return self._decrypt_play_url(encrypted_url)

            return None

        except Exception as e:
            self.log(f"API调用失败: {str(e)}")
            return None

    def _decrypt_play_url(self, encrypted_url):
        """解密播放链接"""
        try:
            # 第一步：Base64解码
            decoded_bytes = base64.b64decode(encrypted_url)

            # 第二步：XOR解密（使用'test'的MD5哈希）
            key = hashlib.md5('test'.encode()).hexdigest()

            xor_result = ''
            for i in range(len(decoded_bytes)):
                k = i % len(key)
                decrypted_char = chr(decoded_bytes[i] ^ ord(key[k]))
                xor_result += decrypted_char

            # 第三步：再次Base64解码
            final_decoded = base64.b64decode(xor_result).decode('utf-8')

            # 第四步：分割成3部分
            parts = final_decoded.split('/')
            if len(parts) != 3:
                return None

            # 第五步：解码各部分
            mapping1 = json.loads(base64.b64decode(parts[0]).decode('utf-8'))
            mapping2 = json.loads(base64.b64decode(parts[1]).decode('utf-8'))
            encrypted_text = base64.b64decode(parts[2]).decode('utf-8')

            # 第六步：反向字符替换（mapping2 -> mapping1）
            result = ''
            for char in encrypted_text:
                if char.isalpha() and char in mapping2:
                    index = mapping2.index(char)
                    result += mapping1[index]
                else:
                    result += char

            return result

        except Exception as e:
            self.log(f"解密失败: {str(e)}")
            return None
